<?php
/**
 * Debug Session 2FA Status
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
echo "<h1>Session 2FA Debug</h1>";

require_once __DIR__ . '/../config/app.php';

echo "<h2>Current Session Data</h2>";
echo "<pre>";
foreach ($_SESSION as $key => $value) {
    if (is_array($value) || is_object($value)) {
        echo "$key: " . print_r($value, true) . "\n";
    } else {
        echo "$key: " . htmlspecialchars($value) . "\n";
    }
}
echo "</pre>";

if (isset($_SESSION['user_id'])) {
    echo "<h2>2FA Status Check</h2>";
    
    require_once __DIR__ . '/../includes/admin_2fa_functions.php';
    
    $userId = $_SESSION['user_id'];
    echo "<p><strong>User ID:</strong> $userId</p>";
    
    // Check if 2FA is enabled
    $is2FAEnabled = isAdmin2FAEnabled($userId);
    echo "<p><strong>2FA Enabled:</strong> " . ($is2FAEnabled ? 'Yes' : 'No') . "</p>";
    
    // Check session 2FA flags
    echo "<p><strong>requires_2fa:</strong> " . (isset($_SESSION['requires_2fa']) ? ($_SESSION['requires_2fa'] ? 'Yes' : 'No') : 'Not set') . "</p>";
    echo "<p><strong>is_2fa_verified:</strong> " . (isset($_SESSION['is_2fa_verified']) ? ($_SESSION['is_2fa_verified'] ? 'Yes' : 'No') : 'Not set') . "</p>";
    echo "<p><strong>2fa_expires:</strong> " . (isset($_SESSION['2fa_expires']) ? date('Y-m-d H:i:s', $_SESSION['2fa_expires']) : 'Not set') . "</p>";
    
    // Check authentication status
    global $auth;
    $isAuthenticated = $auth->isAuthenticated();
    echo "<p><strong>Is Authenticated:</strong> " . ($isAuthenticated ? 'Yes' : 'No') . "</p>";
    
    // Check if logged in
    $isLoggedIn = isLoggedIn();
    echo "<p><strong>Is Logged In:</strong> " . ($isLoggedIn ? 'Yes' : 'No') . "</p>";
    
    echo "<h2>2FA Settings from Database</h2>";
    global $database;
    $settings = $database->fetch(
        "SELECT * FROM admin_2fa_settings WHERE admin_id = ?",
        [$userId]
    );
    
    if ($settings) {
        echo "<pre>";
        print_r($settings);
        echo "</pre>";
    } else {
        echo "<p>No 2FA settings found in database</p>";
    }
    
    echo "<h2>Session from Database</h2>";
    if (isset($_SESSION['session_token'])) {
        $sessionData = $database->fetch(
            "SELECT * FROM sessions WHERE session_token = ?",
            [$_SESSION['session_token']]
        );
        
        if ($sessionData) {
            echo "<pre>";
            print_r($sessionData);
            echo "</pre>";
        } else {
            echo "<p>No session found in database</p>";
        }
    } else {
        echo "<p>No session token in session</p>";
    }
}

echo "<hr>";
echo "<p><a href='/flix/admin/profile/index.php'>← Back to Profile</a></p>";
?>
