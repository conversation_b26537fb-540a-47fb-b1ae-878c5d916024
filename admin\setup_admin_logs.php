<?php
/**
 * Setup script to create admin_logs table
 * Run this once to create the audit logging table
 */

require_once __DIR__ . '/../config/app.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    die('Access denied. Admin access required.');
}

try {
    // Create admin_logs table
    $sql = "CREATE TABLE IF NOT EXISTS admin_logs (
        id VARCHAR(36) PRIMARY KEY,
        admin_id VARCHAR(36) NOT NULL,
        action VARCHAR(100) NOT NULL,
        description TEXT NOT NULL,
        metadata JSON NULL,
        ip_address VARCHAR(45) NULL,
        user_agent TEXT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_admin_logs_admin_id (admin_id),
        INDEX idx_admin_logs_action (action),
        INDEX idx_admin_logs_created_at (created_at),
        FOREI<PERSON><PERSON> KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE
    )";
    
    $database->execute($sql);
    
    echo "<h2>Success!</h2>";
    echo "<p>Admin logs table created successfully.</p>";
    echo "<p><a href='/admin/profile/'>Go to Admin Profile</a></p>";
    
} catch (Exception $e) {
    echo "<h2>Error!</h2>";
    echo "<p>Failed to create admin_logs table: " . htmlspecialchars($e->getMessage()) . "</p>";
    
    // Check if table already exists
    try {
        $result = $database->fetch("SHOW TABLES LIKE 'admin_logs'");
        if ($result) {
            echo "<p>Note: admin_logs table already exists.</p>";
            echo "<p><a href='/admin/profile/'>Go to Admin Profile</a></p>";
        }
    } catch (Exception $e2) {
        echo "<p>Could not check if table exists.</p>";
    }
}
?>
