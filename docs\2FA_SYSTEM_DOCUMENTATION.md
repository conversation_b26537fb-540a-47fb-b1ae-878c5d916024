# Two-Factor Authentication (2FA) System Documentation
## Flix Salonce - Admin Security Enhancement

### Overview
The 2FA system provides enhanced security for admin users in the Flix Salonce salon management system. It implements two practical authentication methods suitable for salon business environments:

1. **Email Verification Codes** - 6-digit codes sent to admin email
2. **Backup Codes** - Emergency access codes for account recovery

### Features
- **Mandatory 2FA**: When enabled, admins must complete 2FA verification after password login
- **Rate Limiting**: 5 attempts per 15-minute window to prevent brute force attacks
- **Secure Storage**: Backup codes are hashed using P<PERSON>'s `password_hash()`
- **Session Security**: Proper session state management with 15-minute timeout for 2FA sessions
- **Audit Trail**: Complete logging of all 2FA events for security monitoring
- **Backward Compatibility**: Admins without 2FA continue to work normally

### Database Schema
The system adds 5 new tables to the database:

#### 1. admin_2fa_settings
Stores 2FA configuration for each admin user.
```sql
- id (VARCHAR(36)) - Primary key
- admin_id (VARCHAR(36)) - Foreign key to users table
- is_enabled (TINYINT(1)) - Whether 2FA is enabled
- email_2fa_enabled (TINYINT(1)) - Email verification enabled
- backup_codes_enabled (TINYINT(1)) - Backup codes enabled
- backup_codes_generated_at (TIMESTAMP) - When backup codes were last generated
```

#### 2. admin_2fa_email_codes
Stores temporary email verification codes.
```sql
- id (VARCHAR(36)) - Primary key
- admin_id (VARCHAR(36)) - Foreign key to users table
- code (VARCHAR(6)) - 6-digit verification code
- expires_at (TIMESTAMP) - Code expiration time (10 minutes)
- is_used (TINYINT(1)) - Whether code has been used
- attempts (INT) - Number of verification attempts
```

#### 3. admin_2fa_backup_codes
Stores emergency backup codes.
```sql
- id (VARCHAR(36)) - Primary key
- admin_id (VARCHAR(36)) - Foreign key to users table
- code_hash (VARCHAR(255)) - Hashed backup code
- is_used (TINYINT(1)) - Whether code has been used
- used_at (TIMESTAMP) - When code was used
```

#### 4. admin_2fa_attempts
Tracks failed attempts for rate limiting.
```sql
- id (VARCHAR(36)) - Primary key
- admin_id (VARCHAR(36)) - Foreign key to users table
- attempt_type (ENUM) - Type of attempt (EMAIL_CODE, BACKUP_CODE)
- ip_address (VARCHAR(45)) - IP address of attempt
- attempted_at (TIMESTAMP) - When attempt was made
```

#### 5. admin_2fa_logs
Comprehensive audit trail for all 2FA events.
```sql
- id (VARCHAR(36)) - Primary key
- admin_id (VARCHAR(36)) - Foreign key to users table
- action (VARCHAR(50)) - Action performed
- details (TEXT) - Additional details in JSON format
- ip_address (VARCHAR(45)) - IP address
- user_agent (TEXT) - Browser user agent
- created_at (TIMESTAMP) - When action occurred
```

### Installation

#### 1. Database Setup
Run the SQL migration to create required tables:
```bash
mysql -u root -p flix_salonce < database/create_2fa_tables.sql
```

#### 2. File Structure
The 2FA system consists of these key files:
- `includes/admin_2fa_functions.php` - Core 2FA functionality
- `admin/auth/verify-2fa.php` - 2FA verification interface
- `admin/auth/backup-codes.php` - Backup codes management
- `admin/profile/index.php` - 2FA settings in admin profile
- `database/create_2fa_tables.sql` - Database schema

### How It Works

#### Login Flow
1. **Standard Login**: Admin enters email and password
2. **2FA Check**: System checks if admin has 2FA enabled
3. **Redirect**: If 2FA enabled, redirects to verification page
4. **Verification**: Admin enters email code or backup code
5. **Complete**: Full session created after successful verification

#### Email Verification
- 6-digit codes generated using `random_int(100000, 999999)`
- Codes expire after 10 minutes
- Single-use only (marked as used after verification)
- Rate limited to 5 attempts per 15-minute window

#### Backup Codes
- 10 codes generated using secure random alphanumeric strings
- Each code is 8 characters long (uppercase letters and numbers)
- Codes are hashed before storage using `password_hash()`
- Single-use only (marked as used after verification)
- Warning system when fewer than 3 codes remain

### Admin Interface

#### Enabling 2FA
1. Go to Admin Profile → Security Settings
2. Click "Enable 2FA" button
3. Select desired methods (Email + Backup Codes recommended)
4. System generates backup codes for secure storage
5. 2FA is now active for future logins

#### Managing 2FA
- **Status Display**: Shows current 2FA status and enabled methods
- **Backup Codes**: View remaining count and generate new codes
- **Disable 2FA**: Option to disable with security warning
- **Audit Trail**: All actions are logged for security monitoring

### Security Features

#### Rate Limiting
- 5 failed attempts per 15-minute window per admin
- Applies to both email codes and backup codes
- IP address tracking for additional security
- Automatic cleanup of old attempt records

#### Session Security
- 2FA sessions timeout after 15 minutes
- Session regeneration on successful verification
- Proper cleanup of temporary session data
- Protection against session fixation attacks

#### Audit Logging
All 2FA events are logged including:
- 2FA enable/disable actions
- Code generation and verification attempts
- Successful and failed login attempts
- Backup code usage and generation
- IP addresses and user agents

### Troubleshooting

#### Common Issues
1. **"Authentication Error"**: Ensure 2FA database tables are created
2. **Email codes not received**: Check email configuration and spam folders
3. **Backup codes not working**: Ensure codes are entered exactly as displayed
4. **Rate limiting**: Wait 15 minutes after 5 failed attempts

#### Debug Mode
Enable debug logging by setting production mode to false in config.
Logs are written to PHP error log for troubleshooting.

### Maintenance

#### Cleanup Tasks
The system includes automatic cleanup functions:
- Expired email codes (older than 10 minutes)
- Old rate limiting attempts (older than 24 hours)
- Old audit logs (older than 90 days)

#### Monitoring
Regular monitoring should include:
- Failed 2FA attempts in audit logs
- Backup code usage patterns
- Admin accounts with low backup codes
- Unusual IP address patterns in logs

### Future Enhancements
Potential improvements for future versions:
- TOTP/Authenticator app support
- SMS verification codes
- Hardware security key support
- Admin-configurable rate limiting
- Email templates customization
- Bulk 2FA management for multiple admins
