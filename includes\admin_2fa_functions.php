<?php
/**
 * Admin Two-Factor Authentication (2FA) Functions
 * Flix Salonce - Enhanced Security System
 */

require_once __DIR__ . '/email_functions.php';

/**
 * Check if admin has 2FA enabled
 */
function isAdmin2FAEnabled($adminId, $db = null) {
    global $database;

    try {
        // Use provided database connection or global one
        $dbConnection = $db ?: $database;

        // Check if database connection exists
        if (!$dbConnection) {
            error_log("Database connection not available in isAdmin2FAEnabled");
            return false;
        }

        $settings = $dbConnection->fetch(
            "SELECT is_enabled FROM admin_2fa_settings WHERE admin_id = ?",
            [$adminId]
        );

        return $settings ? (bool)$settings['is_enabled'] : false;

    } catch (Exception $e) {
        error_log("Error checking 2FA status for admin $adminId: " . $e->getMessage());
        return false;
    }
}

/**
 * Get admin 2FA settings
 */
function getAdmin2FASettings($adminId) {
    global $database;
    
    $settings = $database->fetch(
        "SELECT * FROM admin_2fa_settings WHERE admin_id = ?",
        [$adminId]
    );
    
    if (!$settings) {
        // Create default settings
        $settingsId = generateUUID();
        $database->query(
            "INSERT INTO admin_2fa_settings (id, admin_id, is_enabled, email_2fa_enabled, backup_codes_enabled) 
             VALUES (?, ?, 0, 0, 0)",
            [$settingsId, $adminId]
        );
        
        return [
            'id' => $settingsId,
            'admin_id' => $adminId,
            'is_enabled' => false,
            'email_2fa_enabled' => false,
            'backup_codes_enabled' => false,
            'backup_codes_generated_at' => null
        ];
    }
    
    return $settings;
}

/**
 * Generate 6-digit verification code
 */
function generate2FACode() {
    return str_pad(random_int(100000, 999999), 6, '0', STR_PAD_LEFT);
}

/**
 * Generate backup codes
 */
function generateBackupCodes($count = 10) {
    $codes = [];
    for ($i = 0; $i < $count; $i++) {
        // Generate 8-character alphanumeric codes
        $codes[] = strtoupper(substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 8));
    }
    return $codes;
}

/**
 * Send email verification code
 */
function sendAdmin2FAEmailCode($adminId) {
    global $database;
    
    // Check rate limiting
    if (!checkAdmin2FARateLimit($adminId, 'EMAIL_CODE')) {
        return ['success' => false, 'error' => 'Too many attempts. Please wait before requesting another code.'];
    }
    
    // Get admin details
    $admin = $database->fetch(
        "SELECT name, email FROM users WHERE id = ? AND role = 'ADMIN'",
        [$adminId]
    );
    
    if (!$admin) {
        return ['success' => false, 'error' => 'Admin not found'];
    }
    
    // Invalidate any existing codes
    $database->query(
        "UPDATE admin_2fa_email_codes SET is_used = 1 WHERE admin_id = ? AND is_used = 0",
        [$adminId]
    );
    
    // Generate new code
    $code = generate2FACode();
    $codeId = generateUUID();
    $expiresAt = date('Y-m-d H:i:s', strtotime('+10 minutes'));
    
    // Store code in database
    $database->query(
        "INSERT INTO admin_2fa_email_codes (id, admin_id, code, expires_at, ip_address, user_agent) 
         VALUES (?, ?, ?, ?, ?, ?)",
        [
            $codeId,
            $adminId,
            $code,
            $expiresAt,
            $_SERVER['REMOTE_ADDR'] ?? null,
            $_SERVER['HTTP_USER_AGENT'] ?? null
        ]
    );
    
    // Send email
    $emailSent = sendAdminEmail(
        $admin['email'],
        'Two-Factor Authentication Code - Flix Salonce',
        'admin_2fa_email_code',
        [
            'admin_name' => $admin['name'],
            'verification_code' => $code,
            'expires_minutes' => 10,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'Unknown',
            'timestamp' => date('Y-m-d H:i:s')
        ]
    );
    
    if ($emailSent) {
        // Log the action
        log2FAAction($adminId, '2FA_EMAIL_SENT', 'Email verification code sent', [
            'code_id' => $codeId,
            'expires_at' => $expiresAt
        ]);
        
        return ['success' => true, 'expires_at' => $expiresAt];
    } else {
        // Delete the code if email failed
        $database->query("DELETE FROM admin_2fa_email_codes WHERE id = ?", [$codeId]);
        return ['success' => false, 'error' => 'Failed to send verification email'];
    }
}

/**
 * Verify email 2FA code
 */
function verifyAdmin2FAEmailCode($adminId, $code) {
    global $database;
    
    // Check rate limiting
    if (!checkAdmin2FARateLimit($adminId, 'EMAIL_CODE')) {
        return ['success' => false, 'error' => 'Too many attempts. Please wait before trying again.'];
    }
    
    // Find valid code
    $codeRecord = $database->fetch(
        "SELECT * FROM admin_2fa_email_codes 
         WHERE admin_id = ? AND code = ? AND is_used = 0 AND expires_at > NOW()
         ORDER BY created_at DESC LIMIT 1",
        [$adminId, $code]
    );
    
    // Log attempt
    logAdmin2FAAttempt($adminId, 'EMAIL_CODE', $codeRecord ? true : false);
    
    if (!$codeRecord) {
        log2FAAction($adminId, '2FA_EMAIL_FAILED', 'Invalid or expired email verification code', [
            'attempted_code' => $code
        ]);
        return ['success' => false, 'error' => 'Invalid or expired verification code'];
    }
    
    // Mark code as used
    $database->query(
        "UPDATE admin_2fa_email_codes SET is_used = 1, attempts = attempts + 1 WHERE id = ?",
        [$codeRecord['id']]
    );
    
    // Log successful verification
    log2FAAction($adminId, '2FA_EMAIL_SUCCESS', 'Email verification code verified successfully', [
        'code_id' => $codeRecord['id']
    ]);
    
    return ['success' => true];
}

/**
 * Store backup codes for admin
 */
function storeAdminBackupCodes($adminId, $codes) {
    global $database;
    
    try {
        // Delete existing backup codes
        $database->query("DELETE FROM admin_2fa_backup_codes WHERE admin_id = ?", [$adminId]);
        
        // Store new backup codes
        foreach ($codes as $code) {
            $database->query(
                "INSERT INTO admin_2fa_backup_codes (id, admin_id, code_hash) VALUES (?, ?, ?)",
                [generateUUID(), $adminId, password_hash($code, PASSWORD_DEFAULT)]
            );
        }
        
        // Update settings
        $database->query(
            "UPDATE admin_2fa_settings SET backup_codes_enabled = 1, backup_codes_generated_at = NOW() 
             WHERE admin_id = ?",
            [$adminId]
        );
        
        log2FAAction($adminId, '2FA_BACKUP_GENERATED', 'Backup codes generated', [
            'codes_count' => count($codes)
        ]);
        
        return ['success' => true];
        
    } catch (Exception $e) {
        error_log("Failed to store backup codes: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to store backup codes'];
    }
}

/**
 * Verify backup code
 */
function verifyAdminBackupCode($adminId, $code) {
    global $database;
    
    // Check rate limiting
    if (!checkAdmin2FARateLimit($adminId, 'BACKUP_CODE')) {
        return ['success' => false, 'error' => 'Too many attempts. Please wait before trying again.'];
    }
    
    // Get unused backup codes
    $backupCodes = $database->fetchAll(
        "SELECT * FROM admin_2fa_backup_codes WHERE admin_id = ? AND is_used = 0",
        [$adminId]
    );
    
    $isValid = false;
    $usedCodeId = null;
    
    foreach ($backupCodes as $backupCode) {
        if (password_verify($code, $backupCode['code_hash'])) {
            $isValid = true;
            $usedCodeId = $backupCode['id'];
            break;
        }
    }
    
    // Log attempt
    logAdmin2FAAttempt($adminId, 'BACKUP_CODE', $isValid);
    
    if (!$isValid) {
        log2FAAction($adminId, '2FA_BACKUP_FAILED', 'Invalid backup code attempted', [
            'attempted_code' => substr($code, 0, 2) . '****'
        ]);
        return ['success' => false, 'error' => 'Invalid backup code'];
    }
    
    // Mark backup code as used
    $database->query(
        "UPDATE admin_2fa_backup_codes SET is_used = 1, used_at = NOW(), used_ip = ? WHERE id = ?",
        [$_SERVER['REMOTE_ADDR'] ?? null, $usedCodeId]
    );
    
    // Check remaining backup codes
    $remainingCodes = $database->fetch(
        "SELECT COUNT(*) as count FROM admin_2fa_backup_codes WHERE admin_id = ? AND is_used = 0",
        [$adminId]
    );
    
    log2FAAction($adminId, '2FA_BACKUP_SUCCESS', 'Backup code used successfully', [
        'code_id' => $usedCodeId,
        'remaining_codes' => $remainingCodes['count']
    ]);
    
    return [
        'success' => true,
        'remaining_codes' => $remainingCodes['count']
    ];
}

/**
 * Check rate limiting for 2FA attempts
 */
function checkAdmin2FARateLimit($adminId, $attemptType, $maxAttempts = 5, $timeWindow = 900) {
    global $database;

    $since = date('Y-m-d H:i:s', time() - $timeWindow);

    $attempts = $database->fetch(
        "SELECT COUNT(*) as count FROM admin_2fa_attempts
         WHERE (admin_id = ? OR ip_address = ?) AND attempt_type = ? AND attempted_at > ?",
        [$adminId, $_SERVER['REMOTE_ADDR'] ?? '', $attemptType, $since]
    );

    return $attempts['count'] < $maxAttempts;
}

/**
 * Log 2FA attempt
 */
function logAdmin2FAAttempt($adminId, $attemptType, $isSuccessful) {
    global $database;

    try {
        $database->query(
            "INSERT INTO admin_2fa_attempts (id, admin_id, ip_address, attempt_type, is_successful)
             VALUES (?, ?, ?, ?, ?)",
            [
                generateUUID(),
                $adminId,
                $_SERVER['REMOTE_ADDR'] ?? null,
                $attemptType,
                $isSuccessful ? 1 : 0
            ]
        );
    } catch (Exception $e) {
        error_log("Failed to log 2FA attempt: " . $e->getMessage());
    }
}

/**
 * Log 2FA action for audit trail
 */
function log2FAAction($adminId, $action, $description, $metadata = null) {
    global $database;

    try {
        $database->query(
            "INSERT INTO admin_2fa_logs (id, admin_id, action, description, metadata, ip_address, user_agent)
             VALUES (?, ?, ?, ?, ?, ?, ?)",
            [
                generateUUID(),
                $adminId,
                $action,
                $description,
                $metadata ? json_encode($metadata) : null,
                $_SERVER['REMOTE_ADDR'] ?? null,
                $_SERVER['HTTP_USER_AGENT'] ?? null
            ]
        );
    } catch (Exception $e) {
        error_log("Failed to log 2FA action: " . $e->getMessage());
    }
}

/**
 * Enable 2FA for admin
 */
function enableAdmin2FA($adminId, $emailEnabled = true, $backupCodesEnabled = true) {
    global $database;

    try {
        $database->query(
            "UPDATE admin_2fa_settings
             SET is_enabled = 1, email_2fa_enabled = ?, backup_codes_enabled = ?, updated_at = NOW()
             WHERE admin_id = ?",
            [$emailEnabled ? 1 : 0, $backupCodesEnabled ? 1 : 0, $adminId]
        );

        log2FAAction($adminId, '2FA_ENABLED', 'Two-factor authentication enabled', [
            'email_enabled' => $emailEnabled,
            'backup_codes_enabled' => $backupCodesEnabled
        ]);

        return ['success' => true];

    } catch (Exception $e) {
        error_log("Failed to enable 2FA: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to enable 2FA'];
    }
}

/**
 * Disable 2FA for admin
 */
function disableAdmin2FA($adminId) {
    global $database;

    try {
        // Disable 2FA
        $database->query(
            "UPDATE admin_2fa_settings
             SET is_enabled = 0, email_2fa_enabled = 0, backup_codes_enabled = 0, updated_at = NOW()
             WHERE admin_id = ?",
            [$adminId]
        );

        // Invalidate existing codes and backup codes
        $database->query("UPDATE admin_2fa_email_codes SET is_used = 1 WHERE admin_id = ?", [$adminId]);
        $database->query("DELETE FROM admin_2fa_backup_codes WHERE admin_id = ?", [$adminId]);

        log2FAAction($adminId, '2FA_DISABLED', 'Two-factor authentication disabled');

        return ['success' => true];

    } catch (Exception $e) {
        error_log("Failed to disable 2FA: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to disable 2FA'];
    }
}

/**
 * Check if session requires 2FA verification
 */
function sessionRequires2FA() {
    return isset($_SESSION['requires_2fa']) && $_SESSION['requires_2fa'] === true;
}

/**
 * Check if session is 2FA verified
 */
function sessionIs2FAVerified() {
    return isset($_SESSION['is_2fa_verified']) && $_SESSION['is_2fa_verified'] === true;
}

/**
 * Mark session as requiring 2FA
 */
function markSessionRequires2FA($adminId) {
    $_SESSION['requires_2fa'] = true;
    $_SESSION['is_2fa_verified'] = false;
    $_SESSION['pending_2fa_admin_id'] = $adminId;
}

/**
 * Mark session as 2FA verified
 */
function markSession2FAVerified() {
    $_SESSION['requires_2fa'] = false;
    $_SESSION['is_2fa_verified'] = true;
    unset($_SESSION['pending_2fa_admin_id']);
}

/**
 * Clear 2FA session data
 */
function clear2FASessionData() {
    unset($_SESSION['requires_2fa']);
    unset($_SESSION['is_2fa_verified']);
    unset($_SESSION['pending_2fa_admin_id']);
}

/**
 * Get remaining backup codes count
 */
function getRemainingBackupCodesCount($adminId) {
    global $database;

    $result = $database->fetch(
        "SELECT COUNT(*) as count FROM admin_2fa_backup_codes WHERE admin_id = ? AND is_used = 0",
        [$adminId]
    );

    return $result['count'];
}

/**
 * Clean up expired 2FA sessions and codes
 */
function cleanup2FAExpiredData() {
    global $database;

    try {
        // Clean up expired email codes (older than 10 minutes)
        $database->query(
            "DELETE FROM admin_2fa_email_codes WHERE created_at < DATE_SUB(NOW(), INTERVAL 10 MINUTE)"
        );

        // Clean up old rate limiting attempts (older than 24 hours)
        $database->query(
            "DELETE FROM admin_2fa_attempts WHERE attempted_at < DATE_SUB(NOW(), INTERVAL 24 HOUR)"
        );

        // Clean up old audit logs (older than 90 days)
        $database->query(
            "DELETE FROM admin_2fa_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY)"
        );

        return true;

    } catch (Exception $e) {
        error_log("Failed to cleanup 2FA expired data: " . $e->getMessage());
        return false;
    }
}

/**
 * Check if admin session requires 2FA verification
 */
function requiresAdmin2FAVerification() {
    return isset($_SESSION['requires_2fa']) && $_SESSION['requires_2fa'] &&
           (!isset($_SESSION['is_2fa_verified']) || !$_SESSION['is_2fa_verified']);
}

/**
 * Get 2FA session status
 */
function get2FASessionStatus() {
    if (!isset($_SESSION['requires_2fa']) || !$_SESSION['requires_2fa']) {
        return ['status' => 'none', 'message' => 'No 2FA required'];
    }

    if (isset($_SESSION['2fa_expires']) && time() > $_SESSION['2fa_expires']) {
        return ['status' => 'expired', 'message' => '2FA session expired'];
    }

    if (!isset($_SESSION['is_2fa_verified']) || !$_SESSION['is_2fa_verified']) {
        return ['status' => 'pending', 'message' => '2FA verification required'];
    }

    return ['status' => 'verified', 'message' => '2FA verification complete'];
}

/**
 * Send admin email using template
 */
function sendAdminEmail($to, $subject, $template, $data = []) {
    // This function should integrate with your existing email system
    // For now, using a simple implementation

    $templates = [
        'admin_2fa_email_code' => "
            <h2>Two-Factor Authentication Code</h2>
            <p>Hello {admin_name},</p>
            <p>Your verification code for Flix Salonce admin access is:</p>
            <h1 style='font-size: 32px; color: #f59e0b; text-align: center; letter-spacing: 4px;'>{verification_code}</h1>
            <p>This code will expire in {expires_minutes} minutes.</p>
            <p><strong>Security Information:</strong></p>
            <ul>
                <li>IP Address: {ip_address}</li>
                <li>Time: {timestamp}</li>
            </ul>
            <p>If you did not request this code, please contact your system administrator immediately.</p>
            <p>Best regards,<br>Flix Salonce Security Team</p>
        "
    ];

    if (!isset($templates[$template])) {
        return false;
    }

    $body = $templates[$template];
    foreach ($data as $key => $value) {
        $body = str_replace('{' . $key . '}', $value, $body);
    }

    // Use your existing email function here
    // For now, using PHP's mail function as fallback
    $headers = "MIME-Version: 1.0" . "\r\n";
    $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
    $headers .= "From: <EMAIL>" . "\r\n";

    return mail($to, $subject, $body, $headers);
}
?>
