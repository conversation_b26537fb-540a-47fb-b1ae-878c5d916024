# 2FA Live Server Deployment Guide

## Issue Resolution: Foreign Key Error #1824

**Problem**: `Failed to open the referenced table 'admins'`

**Root Cause**: The original documentation incorrectly referenced a non-existent `admins` table. Your system uses a `users` table with role-based access where admin users have `role = 'ADMIN'`.

## Quick Fix for Live Server

### Step 1: Upload Corrected SQL File
Upload the corrected SQL file to your live server:
- File: `database/deploy_2fa_live_server.sql`
- This file has been corrected to reference `users(id)` instead of `admins(id)`

### Step 2: Execute Database Migration
Run this command on your live server:

```bash
mysql -u your_username -p your_database_name < database/deploy_2fa_live_server.sql
```

Replace:
- `your_username` with your database username
- `your_database_name` with your database name

### Step 3: Verify Tables Created
Check that all tables were created successfully:

```sql
SHOW TABLES LIKE 'admin_2fa%';
```

You should see:
- `admin_2fa_settings`
- `admin_2fa_email_codes` 
- `admin_2fa_backup_codes`
- `admin_2fa_attempts`
- `admin_2fa_logs`

### Step 4: Verify Foreign Keys
Check that foreign keys reference the correct table:

```sql
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE TABLE_SCHEMA = 'your_database_name' 
AND TABLE_NAME LIKE 'admin_2fa%'
AND REFERENCED_TABLE_NAME IS NOT NULL;
```

All foreign keys should reference `users` table, not `admins`.

## Database Schema Differences

### ❌ Incorrect (Original)
```sql
FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE CASCADE
```

### ✅ Correct (Fixed)
```sql
FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE
```

## Admin User Identification

Your system identifies admin users by:
```sql
SELECT id, name, email FROM users WHERE role = 'ADMIN';
```

## Files Updated

1. **database/deploy_2fa_live_server.sql** - Corrected SQL for live deployment
2. **docs/2FA_Implementation_Guide.md** - Updated with correct schema
3. **docs/2FA_Live_Server_Deployment.md** - This deployment guide

## Testing After Deployment

1. **Login as Admin**: Verify admin login still works
2. **Access 2FA Settings**: Go to Admin Profile → 2FA Settings
3. **Enable 2FA**: Test enabling 2FA for an admin user
4. **Generate Backup Codes**: Test backup codes generation
5. **2FA Login Flow**: Test complete 2FA authentication

## Troubleshooting

### If Tables Already Exist
If you get "table already exists" errors, you can:

1. **Drop existing tables** (⚠️ **WARNING**: This will delete 2FA data):
```sql
DROP TABLE IF EXISTS admin_2fa_logs;
DROP TABLE IF EXISTS admin_2fa_attempts;
DROP TABLE IF EXISTS admin_2fa_backup_codes;
DROP TABLE IF EXISTS admin_2fa_email_codes;
DROP TABLE IF EXISTS admin_2fa_settings;
```

2. **Then re-run the deployment script**

### If Foreign Key Errors Persist
Check your database engine:
```sql
SHOW TABLE STATUS WHERE Name = 'users';
```

Ensure it's using InnoDB (required for foreign keys).

## Support

If you encounter any issues:
1. Check error logs in your hosting control panel
2. Verify database user has CREATE and ALTER privileges
3. Ensure the `users` table exists and has the correct structure
4. Contact your hosting provider if database permissions are restricted
