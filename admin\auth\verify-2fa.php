<?php
/**
 * Admin 2FA Verification Page
 * Flix Salon & SPA - Enhanced Security
 */

require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/admin_2fa_functions.php';

// Check if user needs 2FA verification
if (!sessionRequires2FA() || !isset($_SESSION['pending_2fa_admin_id'])) {
    redirect('/admin');
}

$adminId = $_SESSION['pending_2fa_admin_id'];
$error = '';
$success = '';
$showBackupForm = false;

// Get admin details
$admin = $database->fetch(
    "SELECT name, email FROM users WHERE id = ? AND role = 'ADMIN'",
    [$adminId]
);

if (!$admin) {
    clear2FASessionData();
    redirect('/auth/login.php');
}

// Get 2FA settings
$settings = getAdmin2FASettings($adminId);

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'verify_email_code') {
        $code = trim($_POST['code'] ?? '');
        
        if (empty($code)) {
            $error = 'Please enter the verification code';
        } else {
            $result = verifyAdmin2FAEmailCode($adminId, $code);

            if ($result['success']) {
                // Complete 2FA verification and create full session
                $authResult = $auth->complete2FAVerification();

                if ($authResult['success']) {
                    log2FAAction($adminId, '2FA_LOGIN_SUCCESS', 'Admin successfully completed 2FA verification');
                    redirect('/admin');
                } else {
                    $error = $authResult['error'];
                }
            } else {
                $error = $result['error'];
            }
        }
    }
    
    elseif ($action === 'verify_backup_code') {
        $backupCode = trim($_POST['backup_code'] ?? '');
        
        if (empty($backupCode)) {
            $error = 'Please enter a backup code';
        } else {
            $result = verifyAdminBackupCode($adminId, $backupCode);

            if ($result['success']) {
                // Complete 2FA verification and create full session
                $authResult = $auth->complete2FAVerification();

                if ($authResult['success']) {
                    log2FAAction($adminId, '2FA_BACKUP_LOGIN_SUCCESS', 'Admin successfully used backup code for 2FA verification');

                    if ($result['remaining_codes'] <= 2) {
                        $_SESSION['backup_codes_warning'] = "Warning: You have only {$result['remaining_codes']} backup codes remaining. Please generate new ones.";
                    }

                    redirect('/admin');
                } else {
                    $error = $authResult['error'];
                }
            } else {
                $error = $result['error'];
            }
        }
    }
    
    elseif ($action === 'send_email_code') {
        $result = sendAdmin2FAEmailCode($adminId);
        
        if ($result['success']) {
            $success = 'Verification code sent to your email address';
        } else {
            $error = $result['error'];
        }
    }
    
    elseif ($action === 'show_backup_form') {
        $showBackupForm = true;
    }
}

// Auto-send email code if email 2FA is enabled
if ($settings['email_2fa_enabled'] && !isset($_POST['action'])) {
    $result = sendAdmin2FAEmailCode($adminId);
    if ($result['success']) {
        $success = 'Verification code sent to your email address';
    }
}

include __DIR__ . '/../../includes/header.php';
?>

<style>
.auth-main {
    min-height: calc(100vh - 140px);
    background: linear-gradient(135deg, #000000 0%, #0a0a0a 50%, #141414 100%);
    background-attachment: fixed;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 3rem 1rem;
}

.auth-main::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%, rgba(245, 158, 11, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(245, 158, 11, 0.05) 0%, transparent 50%);
    pointer-events: none;
}

.auth-container {
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(245, 158, 11, 0.2);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.8),
                0 0 0 1px rgba(245, 158, 11, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.verification-code-input {
    font-size: 24px;
    letter-spacing: 8px;
    text-align: center;
    font-family: 'Courier New', monospace;
}

.security-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #f59e0b, #d97706);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    box-shadow: 0 10px 25px rgba(245, 158, 11, 0.3);
}
</style>

<div class="auth-main">
    <div class="max-w-md w-full space-y-8">
        <div class="auth-container rounded-2xl p-8 sm:p-10">
            <!-- Security Icon -->
            <div class="security-icon">
                <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                          d="M12 15v2m-6 4h12a2 2 0 002-2v-9a2 2 0 00-2-2H6a2 2 0 00-2 2v9a2 2 0 002 2zm10-12V6a4 4 0 00-8 0v3"></path>
                </svg>
            </div>

            <div class="text-center mb-8">
                <h2 class="text-2xl sm:text-3xl font-bold text-white mb-2">Two-Factor Authentication</h2>
                <p class="text-gray-300 text-sm sm:text-base">
                    Welcome back, <?= htmlspecialchars($admin['name']) ?>. Please verify your identity to continue.
                </p>
            </div>

            <?php if ($error): ?>
                <div class="bg-red-500/20 border border-red-500/50 text-red-200 px-4 py-3 rounded-lg mb-6">
                    <?= htmlspecialchars($error) ?>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="bg-green-500/20 border border-green-500/50 text-green-200 px-4 py-3 rounded-lg mb-6">
                    <?= htmlspecialchars($success) ?>
                </div>
            <?php endif; ?>

            <?php if (!$showBackupForm && $settings['email_2fa_enabled']): ?>
                <!-- Email Verification Form -->
                <form method="POST" class="space-y-6">
                    <input type="hidden" name="action" value="verify_email_code">
                    
                    <div>
                        <label for="code" class="block text-sm font-semibold text-gray-200 mb-2">
                            Enter 6-digit verification code
                        </label>
                        <p class="text-xs text-gray-400 mb-4">
                            Check your email (<?= htmlspecialchars(substr($admin['email'], 0, 3) . '***@' . substr($admin['email'], strpos($admin['email'], '@') + 1)) ?>) for the verification code
                        </p>
                        <input id="code" name="code" type="text" maxlength="6" pattern="[0-9]{6}" required
                               class="verification-code-input appearance-none relative block w-full px-4 py-4 border border-gray-600 placeholder-gray-400 text-white bg-gray-800/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-salon-gold/50 focus:border-salon-gold transition-all duration-300 backdrop-blur-sm"
                               placeholder="000000" autocomplete="off">
                    </div>

                    <button type="submit" 
                            class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-xl text-black bg-salon-gold hover:bg-yellow-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-salon-gold transition-all duration-300 transform hover:scale-105">
                        Verify Code
                    </button>
                </form>

                <div class="mt-6 space-y-4">
                    <form method="POST" class="inline">
                        <input type="hidden" name="action" value="send_email_code">
                        <button type="submit" class="text-salon-gold hover:text-yellow-400 text-sm transition-colors">
                            Resend verification code
                        </button>
                    </form>

                    <?php if ($settings['backup_codes_enabled']): ?>
                        <div class="text-center">
                            <form method="POST" class="inline">
                                <input type="hidden" name="action" value="show_backup_form">
                                <button type="submit" class="text-gray-400 hover:text-white text-sm transition-colors">
                                    Use backup code instead
                                </button>
                            </form>
                        </div>
                    <?php endif; ?>
                </div>

            <?php elseif ($showBackupForm || !$settings['email_2fa_enabled']): ?>
                <!-- Backup Code Form -->
                <form method="POST" class="space-y-6">
                    <input type="hidden" name="action" value="verify_backup_code">
                    
                    <div>
                        <label for="backup_code" class="block text-sm font-semibold text-gray-200 mb-2">
                            Enter backup code
                        </label>
                        <p class="text-xs text-gray-400 mb-4">
                            Use one of your 8-character backup codes
                        </p>
                        <input id="backup_code" name="backup_code" type="text" maxlength="8" required
                               class="appearance-none relative block w-full px-4 py-3 border border-gray-600 placeholder-gray-400 text-white bg-gray-800/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-salon-gold/50 focus:border-salon-gold transition-all duration-300 text-sm backdrop-blur-sm text-center font-mono"
                               placeholder="XXXXXXXX" autocomplete="off" style="letter-spacing: 2px;">
                    </div>

                    <button type="submit" 
                            class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-xl text-black bg-salon-gold hover:bg-yellow-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-salon-gold transition-all duration-300 transform hover:scale-105">
                        Verify Backup Code
                    </button>
                </form>

                <?php if ($settings['email_2fa_enabled']): ?>
                    <div class="mt-6 text-center">
                        <a href="?" class="text-gray-400 hover:text-white text-sm transition-colors">
                            ← Back to email verification
                        </a>
                    </div>
                <?php endif; ?>
            <?php endif; ?>

            <div class="mt-8 text-center">
                <a href="/auth/login.php" class="text-gray-400 hover:text-white text-sm transition-colors">
                    ← Back to login
                </a>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-focus and format verification code input
document.addEventListener('DOMContentLoaded', function() {
    const codeInput = document.getElementById('code');
    if (codeInput) {
        codeInput.focus();
        
        codeInput.addEventListener('input', function(e) {
            // Only allow numbers
            this.value = this.value.replace(/[^0-9]/g, '');
            
            // Auto-submit when 6 digits entered
            if (this.value.length === 6) {
                this.form.submit();
            }
        });
    }
    
    const backupInput = document.getElementById('backup_code');
    if (backupInput) {
        backupInput.focus();
        
        backupInput.addEventListener('input', function(e) {
            // Convert to uppercase and only allow alphanumeric
            this.value = this.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
        });
    }
});
</script>

<?php include __DIR__ . '/../../includes/footer.php'; ?>
