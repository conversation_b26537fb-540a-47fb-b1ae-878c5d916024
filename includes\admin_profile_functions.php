<?php
/**
 * Admin Profile Management Functions
 * Handles admin profile CRUD operations, password changes, and staff email management
 */

/**
 * Get admin profile by ID
 */
function getAdminProfile($userId) {
    global $database;
    
    $sql = "SELECT id, name, email, phone, date_of_birth, image, role, created_at, updated_at 
            FROM users 
            WHERE id = ? AND role = 'ADMIN'";
    
    return $database->fetch($sql, [$userId]);
}

/**
 * Update admin profile
 */
function updateAdminProfile($userId, $data) {
    global $database;
    
    // Validate required fields
    if (empty($data['name'])) {
        throw new Exception("Name is required");
    }
    
    if (empty($data['email'])) {
        throw new Exception("Email is required");
    }
    
    // Validate email format
    if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
        throw new Exception("Invalid email format");
    }
    
    // Check if email already exists (excluding current user)
    $existingUser = $database->fetch(
        "SELECT id FROM users WHERE email = ? AND id != ?",
        [$data['email'], $userId]
    );
    
    if ($existingUser) {
        throw new Exception("Email already exists");
    }
    
    // Validate phone if provided
    if (!empty($data['phone']) && !preg_match('/^[+]?[0-9\s\-\(\)]{10,15}$/', $data['phone'])) {
        throw new Exception("Invalid phone number format");
    }
    
    // Validate date of birth if provided
    if (!empty($data['date_of_birth'])) {
        $dob = DateTime::createFromFormat('Y-m-d', $data['date_of_birth']);
        if (!$dob || $dob->format('Y-m-d') !== $data['date_of_birth']) {
            throw new Exception("Invalid date of birth format");
        }
        
        // Check if date is not in the future
        if ($dob > new DateTime()) {
            throw new Exception("Date of birth cannot be in the future");
        }
    }
    
    $updateFields = [];
    $params = [];
    
    $allowedFields = ['name', 'email', 'phone', 'date_of_birth', 'image'];
    
    foreach ($allowedFields as $field) {
        if (isset($data[$field])) {
            $updateFields[] = "$field = ?";
            $params[] = $data[$field];
        }
    }
    
    if (empty($updateFields)) {
        throw new Exception("No fields to update");
    }
    
    $updateFields[] = "updated_at = NOW()";
    $params[] = $userId;
    
    $sql = "UPDATE users SET " . implode(', ', $updateFields) . " WHERE id = ? AND role = 'ADMIN'";
    
    $database->execute($sql, $params);
    
    // Log the profile update
    logAdminAction($userId, 'PROFILE_UPDATE', 'Admin profile updated', [
        'updated_fields' => array_keys($data)
    ]);
    
    return getAdminProfile($userId);
}

/**
 * Change admin password
 */
function changeAdminPassword($userId, $currentPassword, $newPassword, $confirmPassword) {
    global $database;
    
    // Validate inputs
    if (empty($currentPassword)) {
        throw new Exception("Current password is required");
    }
    
    if (empty($newPassword)) {
        throw new Exception("New password is required");
    }
    
    if ($newPassword !== $confirmPassword) {
        throw new Exception("New password and confirmation do not match");
    }
    
    // Validate password strength
    if (strlen($newPassword) < 8) {
        throw new Exception("Password must be at least 8 characters long");
    }
    
    if (!preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/', $newPassword)) {
        throw new Exception("Password must contain at least one uppercase letter, one lowercase letter, and one number");
    }
    
    // Get current user
    $user = $database->fetch("SELECT password FROM users WHERE id = ? AND role = 'ADMIN'", [$userId]);
    
    if (!$user) {
        throw new Exception("Admin user not found");
    }
    
    // Verify current password
    if (!password_verify($currentPassword, $user['password'])) {
        throw new Exception("Current password is incorrect");
    }
    
    // Hash new password
    $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
    
    // Update password
    $database->execute(
        "UPDATE users SET password = ?, updated_at = NOW() WHERE id = ? AND role = 'ADMIN'",
        [$hashedPassword, $userId]
    );
    
    // Log the password change
    logAdminAction($userId, 'PASSWORD_CHANGE', 'Admin password changed');
    
    return true;
}

/**
 * Change admin email with verification
 */
function changeAdminEmail($userId, $newEmail, $password) {
    global $database;
    
    // Validate inputs
    if (empty($newEmail)) {
        throw new Exception("New email is required");
    }
    
    if (empty($password)) {
        throw new Exception("Password is required for email change");
    }
    
    // Validate email format
    if (!filter_var($newEmail, FILTER_VALIDATE_EMAIL)) {
        throw new Exception("Invalid email format");
    }
    
    // Get current user
    $user = $database->fetch("SELECT email, password FROM users WHERE id = ? AND role = 'ADMIN'", [$userId]);
    
    if (!$user) {
        throw new Exception("Admin user not found");
    }
    
    // Check if email is the same
    if ($user['email'] === $newEmail) {
        throw new Exception("New email must be different from current email");
    }
    
    // Verify password
    if (!password_verify($password, $user['password'])) {
        throw new Exception("Password is incorrect");
    }
    
    // Check if email already exists
    $existingUser = $database->fetch("SELECT id FROM users WHERE email = ?", [$newEmail]);
    
    if ($existingUser) {
        throw new Exception("Email already exists");
    }
    
    // Update email
    $database->execute(
        "UPDATE users SET email = ?, updated_at = NOW() WHERE id = ? AND role = 'ADMIN'",
        [$newEmail, $userId]
    );
    
    // Log the email change
    logAdminAction($userId, 'EMAIL_CHANGE', 'Admin email changed', [
        'old_email' => $user['email'],
        'new_email' => $newEmail
    ]);
    
    return true;
}

/**
 * Get staff member for email management
 */
function getStaffMember($staffId) {
    global $database;
    
    $sql = "SELECT id, name, email, phone, role, is_active, created_at, updated_at 
            FROM users 
            WHERE id = ? AND role = 'STAFF'";
    
    return $database->fetch($sql, [$staffId]);
}

/**
 * Update staff email (admin only)
 */
function updateStaffEmail($staffId, $newEmail, $adminId) {
    global $database;
    
    // Validate inputs
    if (empty($newEmail)) {
        throw new Exception("Email is required");
    }
    
    // Validate email format
    if (!filter_var($newEmail, FILTER_VALIDATE_EMAIL)) {
        throw new Exception("Invalid email format");
    }
    
    // Get staff member
    $staff = getStaffMember($staffId);
    if (!$staff) {
        throw new Exception("Staff member not found");
    }
    
    // Check if email is the same
    if ($staff['email'] === $newEmail) {
        throw new Exception("New email must be different from current email");
    }
    
    // Check if email already exists
    $existingUser = $database->fetch("SELECT id FROM users WHERE email = ?", [$newEmail]);
    
    if ($existingUser) {
        throw new Exception("Email already exists");
    }
    
    // Update email
    $database->execute(
        "UPDATE users SET email = ?, updated_at = NOW() WHERE id = ? AND role = 'STAFF'",
        [$newEmail, $staffId]
    );
    
    // Log the action
    logAdminAction($adminId, 'STAFF_EMAIL_CHANGE', 'Staff email changed by admin', [
        'staff_id' => $staffId,
        'staff_name' => $staff['name'],
        'old_email' => $staff['email'],
        'new_email' => $newEmail
    ]);
    
    return true;
}

/**
 * Log admin actions for audit trail
 */
function logAdminAction($adminId, $action, $description, $metadata = null) {
    global $database;
    
    try {
        $sql = "INSERT INTO admin_logs (id, admin_id, action, description, metadata, ip_address, user_agent, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, NOW())";
        
        $params = [
            generateUUID(),
            $adminId,
            $action,
            $description,
            $metadata ? json_encode($metadata) : null,
            $_SERVER['REMOTE_ADDR'] ?? null,
            $_SERVER['HTTP_USER_AGENT'] ?? null
        ];
        
        $database->execute($sql, $params);
    } catch (Exception $e) {
        // Log error but don't throw exception to avoid breaking the main operation
        error_log("Failed to log admin action: " . $e->getMessage());
    }
}

/**
 * Upload profile image
 */
function uploadProfileImage($file, $userId) {
    // Validate file
    if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
        throw new Exception("No file uploaded");
    }
    
    // Check file size (max 5MB)
    if ($file['size'] > 5 * 1024 * 1024) {
        throw new Exception("File size must be less than 5MB");
    }
    
    // Check file type
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    $mimeType = finfo_file($finfo, $file['tmp_name']);
    finfo_close($finfo);
    
    if (!in_array($mimeType, $allowedTypes)) {
        throw new Exception("Only JPEG, PNG, GIF, and WebP images are allowed");
    }
    
    // Generate unique filename
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = 'profile_' . $userId . '_' . time() . '.' . $extension;
    
    // Create upload directory if it doesn't exist
    $uploadDir = __DIR__ . '/../uploads/profiles/';
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    $uploadPath = $uploadDir . $filename;
    
    // Move uploaded file
    if (!move_uploaded_file($file['tmp_name'], $uploadPath)) {
        throw new Exception("Failed to upload file");
    }
    
    // Return relative URL
    return '/uploads/profiles/' . $filename;
}
