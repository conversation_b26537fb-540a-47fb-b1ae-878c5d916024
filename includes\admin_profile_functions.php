<?php
/**
 * Admin Profile Management Functions
 * Handles admin profile CRUD operations, password changes, and staff email management
 */

/**
 * Get admin profile by ID
 */
function getAdminProfile($userId) {
    global $database;
    
    $sql = "SELECT id, name, email, phone, date_of_birth, image, role, created_at, updated_at 
            FROM users 
            WHERE id = ? AND role = 'ADMIN'";
    
    return $database->fetch($sql, [$userId]);
}

/**
 * Update admin profile
 */
function updateAdminProfile($userId, $data) {
    global $database;

    // Only validate fields that are being updated
    if (isset($data['name']) && empty($data['name'])) {
        throw new Exception("Name is required");
    }

    if (isset($data['email'])) {
        if (empty($data['email'])) {
            throw new Exception("Email is required");
        }

        // Validate email format
        if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            throw new Exception("Invalid email format");
        }

        // Check if email already exists (excluding current user)
        $existingUser = $database->fetch(
            "SELECT id FROM users WHERE email = ? AND id != ?",
            [$data['email'], $userId]
        );

        if ($existingUser) {
            throw new Exception("Email already exists");
        }
    }

    // Validate phone if provided and being updated
    if (isset($data['phone']) && !empty($data['phone']) && !preg_match('/^[+]?[0-9\s\-\(\)]{10,15}$/', $data['phone'])) {
        throw new Exception("Invalid phone number format");
    }

    // Validate date of birth if provided and being updated
    if (isset($data['date_of_birth']) && !empty($data['date_of_birth'])) {
        $dob = DateTime::createFromFormat('Y-m-d', $data['date_of_birth']);
        if (!$dob || $dob->format('Y-m-d') !== $data['date_of_birth']) {
            throw new Exception("Invalid date of birth format");
        }

        // Check if date is not in the future
        if ($dob > new DateTime()) {
            throw new Exception("Date of birth cannot be in the future");
        }
    }
    
    $updateFields = [];
    $params = [];
    
    $allowedFields = ['name', 'email', 'phone', 'date_of_birth', 'image'];
    
    foreach ($allowedFields as $field) {
        if (isset($data[$field])) {
            $updateFields[] = "$field = ?";
            $params[] = $data[$field];
        }
    }
    
    if (empty($updateFields)) {
        throw new Exception("No fields to update");
    }
    
    $updateFields[] = "updated_at = NOW()";
    $params[] = $userId;
    
    $sql = "UPDATE users SET " . implode(', ', $updateFields) . " WHERE id = ? AND role = 'ADMIN'";
    
    $database->execute($sql, $params);
    
    // Log the profile update
    logAdminAction($userId, 'PROFILE_UPDATE', 'Admin profile updated', [
        'updated_fields' => array_keys($data)
    ]);
    
    return getAdminProfile($userId);
}

/**
 * Change admin password
 */
function changeAdminPassword($userId, $currentPassword, $newPassword, $confirmPassword) {
    global $database;
    
    // Validate inputs
    if (empty($currentPassword)) {
        throw new Exception("Current password is required");
    }
    
    if (empty($newPassword)) {
        throw new Exception("New password is required");
    }
    
    if ($newPassword !== $confirmPassword) {
        throw new Exception("New password and confirmation do not match");
    }
    
    // Validate password strength
    if (strlen($newPassword) < 8) {
        throw new Exception("Password must be at least 8 characters long");
    }
    
    if (!preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/', $newPassword)) {
        throw new Exception("Password must contain at least one uppercase letter, one lowercase letter, and one number");
    }
    
    // Get current user
    $user = $database->fetch("SELECT password FROM users WHERE id = ? AND role = 'ADMIN'", [$userId]);
    
    if (!$user) {
        throw new Exception("Admin user not found");
    }
    
    // Verify current password
    if (!password_verify($currentPassword, $user['password'])) {
        throw new Exception("Current password is incorrect");
    }
    
    // Hash new password
    $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
    
    // Update password
    $database->execute(
        "UPDATE users SET password = ?, updated_at = NOW() WHERE id = ? AND role = 'ADMIN'",
        [$hashedPassword, $userId]
    );
    
    // Log the password change
    logAdminAction($userId, 'PASSWORD_CHANGE', 'Admin password changed');
    
    return true;
}

/**
 * Change admin email with verification
 */
function changeAdminEmail($userId, $newEmail, $password) {
    global $database;
    
    // Validate inputs
    if (empty($newEmail)) {
        throw new Exception("New email is required");
    }
    
    if (empty($password)) {
        throw new Exception("Password is required for email change");
    }
    
    // Validate email format
    if (!filter_var($newEmail, FILTER_VALIDATE_EMAIL)) {
        throw new Exception("Invalid email format");
    }
    
    // Get current user
    $user = $database->fetch("SELECT email, password FROM users WHERE id = ? AND role = 'ADMIN'", [$userId]);
    
    if (!$user) {
        throw new Exception("Admin user not found");
    }
    
    // Check if email is the same
    if ($user['email'] === $newEmail) {
        throw new Exception("New email must be different from current email");
    }
    
    // Verify password
    if (!password_verify($password, $user['password'])) {
        throw new Exception("Password is incorrect");
    }
    
    // Check if email already exists
    $existingUser = $database->fetch("SELECT id FROM users WHERE email = ?", [$newEmail]);
    
    if ($existingUser) {
        throw new Exception("Email already exists");
    }
    
    // Update email
    $database->execute(
        "UPDATE users SET email = ?, updated_at = NOW() WHERE id = ? AND role = 'ADMIN'",
        [$newEmail, $userId]
    );
    
    // Log the email change
    logAdminAction($userId, 'EMAIL_CHANGE', 'Admin email changed', [
        'old_email' => $user['email'],
        'new_email' => $newEmail
    ]);
    
    return true;
}

/**
 * Get staff member for email management
 */
function getStaffMember($staffId) {
    global $database;
    
    $sql = "SELECT id, name, email, phone, role, is_active, created_at, updated_at 
            FROM users 
            WHERE id = ? AND role = 'STAFF'";
    
    return $database->fetch($sql, [$staffId]);
}

/**
 * Update staff email (admin only)
 */
function updateStaffEmail($staffId, $newEmail, $adminId) {
    global $database;
    
    // Validate inputs
    if (empty($newEmail)) {
        throw new Exception("Email is required");
    }
    
    // Validate email format
    if (!filter_var($newEmail, FILTER_VALIDATE_EMAIL)) {
        throw new Exception("Invalid email format");
    }
    
    // Get staff member
    $staff = getStaffMember($staffId);
    if (!$staff) {
        throw new Exception("Staff member not found");
    }
    
    // Check if email is the same
    if ($staff['email'] === $newEmail) {
        throw new Exception("New email must be different from current email");
    }
    
    // Check if email already exists
    $existingUser = $database->fetch("SELECT id FROM users WHERE email = ?", [$newEmail]);
    
    if ($existingUser) {
        throw new Exception("Email already exists");
    }
    
    // Update email
    $database->execute(
        "UPDATE users SET email = ?, updated_at = NOW() WHERE id = ? AND role = 'STAFF'",
        [$newEmail, $staffId]
    );
    
    // Log the action
    logAdminAction($adminId, 'STAFF_EMAIL_CHANGE', 'Staff email changed by admin', [
        'staff_id' => $staffId,
        'staff_name' => $staff['name'],
        'old_email' => $staff['email'],
        'new_email' => $newEmail
    ]);
    
    return true;
}

/**
 * Log admin actions for audit trail
 */
function logAdminAction($adminId, $action, $description, $metadata = null) {
    global $database;
    
    try {
        $sql = "INSERT INTO admin_logs (id, admin_id, action, description, metadata, ip_address, user_agent, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, NOW())";
        
        $params = [
            generateUUID(),
            $adminId,
            $action,
            $description,
            $metadata ? json_encode($metadata) : null,
            $_SERVER['REMOTE_ADDR'] ?? null,
            $_SERVER['HTTP_USER_AGENT'] ?? null
        ];
        
        $database->execute($sql, $params);
    } catch (Exception $e) {
        // Log error but don't throw exception to avoid breaking the main operation
        error_log("Failed to log admin action: " . $e->getMessage());
    }
}

/**
 * Create new admin user
 */
function createAdminUser($data, $createdByAdminId) {
    global $database;

    // Validate required fields
    if (empty($data['name'])) {
        throw new Exception("Name is required");
    }

    if (empty($data['email'])) {
        throw new Exception("Email is required");
    }

    if (empty($data['password'])) {
        throw new Exception("Password is required");
    }

    // Validate email format
    if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
        throw new Exception("Invalid email format");
    }

    // Check if email already exists
    $existingUser = $database->fetch("SELECT id FROM users WHERE email = ?", [$data['email']]);
    if ($existingUser) {
        throw new Exception("Email already exists");
    }

    // Validate phone if provided
    if (!empty($data['phone']) && !preg_match('/^[+]?[0-9\s\-\(\)]{10,15}$/', $data['phone'])) {
        throw new Exception("Invalid phone number format");
    }

    // Validate date of birth if provided
    if (!empty($data['date_of_birth'])) {
        $dob = DateTime::createFromFormat('Y-m-d', $data['date_of_birth']);
        if (!$dob || $dob->format('Y-m-d') !== $data['date_of_birth']) {
            throw new Exception("Invalid date of birth format");
        }

        // Check if date is not in the future
        if ($dob > new DateTime()) {
            throw new Exception("Date of birth cannot be in the future");
        }
    }

    // Validate password strength
    if (strlen($data['password']) < 8) {
        throw new Exception("Password must be at least 8 characters long");
    }

    if (!preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/', $data['password'])) {
        throw new Exception("Password must contain at least one uppercase letter, one lowercase letter, and one number");
    }

    // Generate new admin ID
    $adminId = generateUUID();

    // Hash password
    $hashedPassword = password_hash($data['password'], PASSWORD_DEFAULT);

    // Generate referral code
    $referralCode = generateReferralCode();

    // Insert new admin user
    $sql = "INSERT INTO users (id, name, email, password, phone, date_of_birth, role, referral_code, is_active, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, 'ADMIN', ?, 1, NOW(), NOW())";

    $params = [
        $adminId,
        $data['name'],
        $data['email'],
        $hashedPassword,
        $data['phone'] ?: null,
        $data['date_of_birth'] ?: null,
        $referralCode
    ];

    $database->execute($sql, $params);

    // Log the admin creation
    logAdminAction($createdByAdminId, 'ADMIN_CREATE', 'New admin user created', [
        'new_admin_id' => $adminId,
        'new_admin_name' => $data['name'],
        'new_admin_email' => $data['email']
    ]);

    return [
        'id' => $adminId,
        'name' => $data['name'],
        'email' => $data['email'],
        'phone' => $data['phone'] ?: null,
        'date_of_birth' => $data['date_of_birth'] ?: null,
        'role' => 'ADMIN',
        'referral_code' => $referralCode,
        'created_at' => date('Y-m-d H:i:s')
    ];
}

/**
 * Get all admin users
 */
function getAllAdminUsers() {
    global $database;

    $sql = "SELECT id, name, email, phone, date_of_birth, image, is_active, created_at, updated_at
            FROM users
            WHERE role = 'ADMIN'
            ORDER BY created_at DESC";

    return $database->fetchAll($sql);
}

/**
 * Toggle admin user status (activate/deactivate)
 */
function toggleAdminStatus($adminId, $currentAdminId) {
    global $database;

    // Prevent admin from deactivating themselves
    if ($adminId === $currentAdminId) {
        throw new Exception("You cannot deactivate your own account");
    }

    // Get current status
    $admin = $database->fetch("SELECT is_active, name FROM users WHERE id = ? AND role = 'ADMIN'", [$adminId]);
    if (!$admin) {
        throw new Exception("Admin user not found");
    }

    $newStatus = $admin['is_active'] ? 0 : 1;

    // Update status
    $database->execute(
        "UPDATE users SET is_active = ?, updated_at = NOW() WHERE id = ? AND role = 'ADMIN'",
        [$newStatus, $adminId]
    );

    // Log the action
    logAdminAction($currentAdminId, 'ADMIN_STATUS_CHANGE',
        'Admin status changed: ' . ($newStatus ? 'activated' : 'deactivated'), [
        'target_admin_id' => $adminId,
        'target_admin_name' => $admin['name'],
        'new_status' => $newStatus ? 'active' : 'inactive'
    ]);

    return $newStatus;
}

/**
 * Generate referral code for new admin
 */
function generateReferralCode() {
    global $database;

    do {
        $code = 'ADM' . strtoupper(substr(md5(uniqid()), 0, 7));
        $existing = $database->fetch("SELECT id FROM users WHERE referral_code = ?", [$code]);
    } while ($existing);

    return $code;
}

/**
 * Get single admin user by ID
 */
function getAdminUser($adminId) {
    global $database;

    $sql = "SELECT id, name, email, phone, date_of_birth, image, is_active, created_at, updated_at, referral_code
            FROM users
            WHERE id = ? AND role = 'ADMIN'";

    return $database->fetch($sql, [$adminId]);
}

/**
 * Update admin user (by another admin)
 */
function updateAdminUser($adminId, $data, $updatedByAdminId) {
    global $database;

    // Validate required fields
    if (empty($data['name'])) {
        throw new Exception("Name is required");
    }

    if (empty($data['email'])) {
        throw new Exception("Email is required");
    }

    // Validate email format
    if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
        throw new Exception("Invalid email format");
    }

    // Check if admin exists
    $admin = getAdminUser($adminId);
    if (!$admin) {
        throw new Exception("Admin user not found");
    }

    // Check if email already exists (excluding current admin)
    $existingUser = $database->fetch(
        "SELECT id FROM users WHERE email = ? AND id != ?",
        [$data['email'], $adminId]
    );

    if ($existingUser) {
        throw new Exception("Email already exists");
    }

    // Validate phone if provided
    if (!empty($data['phone']) && !preg_match('/^[+]?[0-9\s\-\(\)]{10,15}$/', $data['phone'])) {
        throw new Exception("Invalid phone number format");
    }

    // Validate date of birth if provided
    if (!empty($data['date_of_birth'])) {
        $dob = DateTime::createFromFormat('Y-m-d', $data['date_of_birth']);
        if (!$dob || $dob->format('Y-m-d') !== $data['date_of_birth']) {
            throw new Exception("Invalid date of birth format");
        }

        // Check if date is not in the future
        if ($dob > new DateTime()) {
            throw new Exception("Date of birth cannot be in the future");
        }
    }

    $updateFields = [];
    $params = [];

    $allowedFields = ['name', 'email', 'phone', 'date_of_birth'];

    foreach ($allowedFields as $field) {
        if (isset($data[$field])) {
            $updateFields[] = "$field = ?";
            $params[] = $data[$field];
        }
    }

    if (empty($updateFields)) {
        throw new Exception("No fields to update");
    }

    $updateFields[] = "updated_at = NOW()";
    $params[] = $adminId;

    $sql = "UPDATE users SET " . implode(', ', $updateFields) . " WHERE id = ? AND role = 'ADMIN'";

    $database->execute($sql, $params);

    // Log the admin update
    logAdminAction($updatedByAdminId, 'ADMIN_UPDATE', 'Admin user updated', [
        'target_admin_id' => $adminId,
        'target_admin_name' => $admin['name'],
        'updated_fields' => array_keys($data),
        'old_data' => [
            'name' => $admin['name'],
            'email' => $admin['email'],
            'phone' => $admin['phone'],
            'date_of_birth' => $admin['date_of_birth']
        ],
        'new_data' => $data
    ]);

    return getAdminUser($adminId);
}

/**
 * Delete admin user (soft delete by deactivation)
 */
function deleteAdminUser($adminId, $deletedByAdminId) {
    global $database;

    // Prevent admin from deleting themselves
    if ($adminId === $deletedByAdminId) {
        throw new Exception("You cannot delete your own account");
    }

    // Get admin details
    $admin = getAdminUser($adminId);
    if (!$admin) {
        throw new Exception("Admin user not found");
    }

    // Check if this is the last active admin
    $activeAdminCount = $database->fetch(
        "SELECT COUNT(*) as count FROM users WHERE role = 'ADMIN' AND is_active = 1 AND id != ?",
        [$adminId]
    );

    if ($activeAdminCount['count'] == 0) {
        throw new Exception("Cannot delete the last active admin user");
    }

    // Soft delete by deactivating
    $database->execute(
        "UPDATE users SET is_active = 0, updated_at = NOW() WHERE id = ? AND role = 'ADMIN'",
        [$adminId]
    );

    // Log the deletion
    logAdminAction($deletedByAdminId, 'ADMIN_DELETE', 'Admin user deleted (deactivated)', [
        'target_admin_id' => $adminId,
        'target_admin_name' => $admin['name'],
        'target_admin_email' => $admin['email']
    ]);

    return true;
}

/**
 * Reset admin password (by another admin)
 */
function resetAdminPassword($adminId, $newPassword, $resetByAdminId) {
    global $database;

    // Validate inputs
    if (empty($newPassword)) {
        throw new Exception("New password is required");
    }

    // Validate password strength
    if (strlen($newPassword) < 8) {
        throw new Exception("Password must be at least 8 characters long");
    }

    if (!preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/', $newPassword)) {
        throw new Exception("Password must contain at least one uppercase letter, one lowercase letter, and one number");
    }

    // Get admin details
    $admin = getAdminUser($adminId);
    if (!$admin) {
        throw new Exception("Admin user not found");
    }

    // Hash new password
    $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);

    // Update password
    $database->execute(
        "UPDATE users SET password = ?, updated_at = NOW() WHERE id = ? AND role = 'ADMIN'",
        [$hashedPassword, $adminId]
    );

    // Log the password reset
    logAdminAction($resetByAdminId, 'ADMIN_PASSWORD_RESET', 'Admin password reset by another admin', [
        'target_admin_id' => $adminId,
        'target_admin_name' => $admin['name'],
        'target_admin_email' => $admin['email']
    ]);

    return true;
}

/**
 * Get admin activity logs
 */
function getAdminLogs($adminId = null, $limit = 50, $offset = 0) {
    global $database;

    $sql = "SELECT al.*, u.name as admin_name, u.email as admin_email
            FROM admin_logs al
            LEFT JOIN users u ON al.admin_id = u.id
            WHERE 1=1";

    $params = [];

    if ($adminId) {
        $sql .= " AND al.admin_id = ?";
        $params[] = $adminId;
    }

    $sql .= " ORDER BY al.created_at DESC LIMIT ? OFFSET ?";
    $params[] = $limit;
    $params[] = $offset;

    return $database->fetchAll($sql, $params);
}

/**
 * Get admin statistics
 */
function getAdminStatistics() {
    global $database;

    $stats = [];

    // Total admins
    $result = $database->fetch("SELECT COUNT(*) as count FROM users WHERE role = 'ADMIN'");
    $stats['total_admins'] = $result['count'];

    // Active admins
    $result = $database->fetch("SELECT COUNT(*) as count FROM users WHERE role = 'ADMIN' AND is_active = 1");
    $stats['active_admins'] = $result['count'];

    // Inactive admins
    $stats['inactive_admins'] = $stats['total_admins'] - $stats['active_admins'];

    // Recent admin activities (last 7 days)
    $result = $database->fetch(
        "SELECT COUNT(*) as count FROM admin_logs WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)"
    );
    $stats['recent_activities'] = $result['count'];

    // Most recent admin creation
    $result = $database->fetch(
        "SELECT created_at FROM users WHERE role = 'ADMIN' ORDER BY created_at DESC LIMIT 1"
    );
    $stats['last_admin_created'] = $result ? $result['created_at'] : null;

    return $stats;
}

/**
 * Search admin users
 */
function searchAdminUsers($searchTerm, $status = 'all') {
    global $database;

    $sql = "SELECT id, name, email, phone, date_of_birth, image, is_active, created_at, updated_at
            FROM users
            WHERE role = 'ADMIN'
            AND (name LIKE ? OR email LIKE ?)";

    $params = ["%$searchTerm%", "%$searchTerm%"];

    if ($status === 'active') {
        $sql .= " AND is_active = 1";
    } elseif ($status === 'inactive') {
        $sql .= " AND is_active = 0";
    }

    $sql .= " ORDER BY created_at DESC";

    return $database->fetchAll($sql, $params);
}

/**
 * Bulk update admin status
 */
function bulkUpdateAdminStatus($adminIds, $status, $updatedByAdminId) {
    global $database;

    if (empty($adminIds) || !is_array($adminIds)) {
        throw new Exception("No admin IDs provided");
    }

    // Remove current admin from the list to prevent self-modification
    $adminIds = array_filter($adminIds, function($id) use ($updatedByAdminId) {
        return $id !== $updatedByAdminId;
    });

    if (empty($adminIds)) {
        throw new Exception("Cannot modify your own account status");
    }

    $placeholders = str_repeat('?,', count($adminIds) - 1) . '?';
    $params = array_merge([$status], $adminIds);

    $sql = "UPDATE users SET is_active = ?, updated_at = NOW()
            WHERE role = 'ADMIN' AND id IN ($placeholders)";

    $database->execute($sql, $params);

    // Log the bulk update
    logAdminAction($updatedByAdminId, 'ADMIN_BULK_STATUS_UPDATE',
        'Bulk admin status update: ' . ($status ? 'activated' : 'deactivated'), [
        'target_admin_ids' => $adminIds,
        'new_status' => $status ? 'active' : 'inactive',
        'affected_count' => count($adminIds)
    ]);

    return count($adminIds);
}

/**
 * Upload profile image
 */
function uploadProfileImage($file, $userId) {
    // Validate file
    if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
        throw new Exception("No file uploaded");
    }
    
    // Check file size (max 5MB)
    if ($file['size'] > 5 * 1024 * 1024) {
        throw new Exception("File size must be less than 5MB");
    }
    
    // Check file type
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    $mimeType = finfo_file($finfo, $file['tmp_name']);
    finfo_close($finfo);
    
    if (!in_array($mimeType, $allowedTypes)) {
        throw new Exception("Only JPEG, PNG, GIF, and WebP images are allowed");
    }
    
    // Generate unique filename
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = 'profile_' . $userId . '_' . time() . '.' . $extension;
    
    // Create upload directory if it doesn't exist
    $uploadDir = __DIR__ . '/../uploads/profiles/';
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    $uploadPath = $uploadDir . $filename;
    
    // Move uploaded file
    if (!move_uploaded_file($file['tmp_name'], $uploadPath)) {
        throw new Exception("Failed to upload file");
    }
    
    // Return relative URL
    return '/uploads/profiles/' . $filename;
}
