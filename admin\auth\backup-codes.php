<?php
/**
 * Admin 2FA Backup Codes Management
 * Flix Salon & SPA - Enhanced Security
 */

require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/admin_2fa_functions.php';

// Require admin authentication
if (!isLoggedIn() || !hasRole('ADMIN')) {
    redirect('/auth/login.php');
}

$adminId = getCurrentUser()['id'];
$error = '';
$success = '';
$backupCodes = [];
$showCodes = false;

// Get 2FA settings
$settings = getAdmin2FASettings($adminId);

if (!$settings['is_enabled']) {
    redirect('/admin/profile/index.php?tab=security');
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'generate_backup_codes') {
        // Generate new backup codes
        $newCodes = generateBackupCodes(10);
        $result = storeAdminBackupCodes($adminId, $newCodes);
        
        if ($result['success']) {
            $backupCodes = $newCodes;
            $showCodes = true;
            $success = 'New backup codes generated successfully. Please save them in a secure location.';
        } else {
            $error = $result['error'];
        }
    }
}

// Get remaining backup codes count
$remainingCodes = getRemainingBackupCodesCount($adminId);

include __DIR__ . '/../../includes/header.php';
?>

<style>
.admin-main {
    min-height: calc(100vh - 140px);
    background: linear-gradient(135deg, #000000 0%, #0a0a0a 50%, #141414 100%);
    padding: 2rem 0;
}

.backup-code {
    font-family: 'Courier New', monospace;
    font-size: 18px;
    letter-spacing: 2px;
    padding: 12px 16px;
    background: rgba(245, 158, 11, 0.1);
    border: 1px solid rgba(245, 158, 11, 0.3);
    border-radius: 8px;
    color: #f59e0b;
    text-align: center;
    user-select: all;
    cursor: pointer;
    transition: all 0.3s ease;
}

.backup-code:hover {
    background: rgba(245, 158, 11, 0.2);
    border-color: rgba(245, 158, 11, 0.5);
}

.security-warning {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 38, 0.1));
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.print-button {
    background: linear-gradient(135deg, #374151, #4b5563);
    border: 1px solid #6b7280;
    color: white;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.print-button:hover {
    background: linear-gradient(135deg, #4b5563, #6b7280);
}

@media print {
    body * {
        visibility: hidden;
    }
    .printable, .printable * {
        visibility: visible;
    }
    .printable {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
    }
    .no-print {
        display: none !important;
    }
}
</style>

<div class="admin-main">
    <div class="max-w-4xl mx-auto px-4">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-white mb-2">Backup Codes</h1>
                    <p class="text-gray-400">Manage your two-factor authentication backup codes</p>
                </div>
                <a href="/admin/profile/index.php?tab=security" 
                   class="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors">
                    ← Back to Security
                </a>
            </div>
        </div>

        <?php if ($error): ?>
            <div class="bg-red-500/20 border border-red-500/50 text-red-200 px-4 py-3 rounded-lg mb-6">
                <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>

        <?php if ($success): ?>
            <div class="bg-green-500/20 border border-green-500/50 text-green-200 px-4 py-3 rounded-lg mb-6">
                <?= htmlspecialchars($success) ?>
            </div>
        <?php endif; ?>

        <?php if ($showCodes && !empty($backupCodes)): ?>
            <!-- Display New Backup Codes -->
            <div class="bg-gray-800/50 backdrop-blur-sm border border-gray-700 rounded-xl p-6 mb-8">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-xl font-semibold text-white">Your New Backup Codes</h2>
                    <button onclick="printCodes()" class="print-button no-print">
                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a1 1 0 001-1v-4a1 1 0 00-1-1H9a1 1 0 00-1 1v4a1 1 0 001 1zm3-5h2m-2-3h2"></path>
                        </svg>
                        Print Codes
                    </button>
                </div>

                <div class="security-warning">
                    <div class="flex items-start">
                        <svg class="w-6 h-6 text-red-400 mr-3 mt-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                        <div>
                            <h3 class="text-red-200 font-semibold mb-2">Important Security Notice</h3>
                            <ul class="text-red-300 text-sm space-y-1">
                                <li>• Save these codes in a secure location (password manager, safe, etc.)</li>
                                <li>• Each code can only be used once</li>
                                <li>• These codes will not be shown again</li>
                                <li>• If you lose access to your email, these codes are your only way to log in</li>
                                <li>• Generate new codes if you suspect these have been compromised</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="printable">
                    <div class="text-center mb-6 print-only" style="display: none;">
                        <h1 style="color: #000; margin-bottom: 10px;">Flix Salonce - Admin Backup Codes</h1>
                        <p style="color: #666; font-size: 14px;">Generated: <?= date('Y-m-d H:i:s') ?></p>
                        <p style="color: #666; font-size: 14px;">Admin: <?= htmlspecialchars(getCurrentUser()['name']) ?></p>
                    </div>

                    <div class="grid grid-cols-2 gap-4">
                        <?php foreach ($backupCodes as $index => $code): ?>
                            <div class="backup-code" onclick="selectText(this)" title="Click to select">
                                <?= htmlspecialchars($code) ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>

                <div class="mt-6 text-center no-print">
                    <p class="text-gray-400 text-sm">Click on any code to select it for copying</p>
                </div>
            </div>

        <?php else: ?>
            <!-- Backup Codes Status -->
            <div class="bg-gray-800/50 backdrop-blur-sm border border-gray-700 rounded-xl p-6 mb-8">
                <h2 class="text-xl font-semibold text-white mb-4">Backup Codes Status</h2>
                
                <div class="flex items-center justify-between p-4 bg-gray-700/50 rounded-lg">
                    <div>
                        <h3 class="text-white font-medium">Remaining Backup Codes</h3>
                        <p class="text-gray-400 text-sm">
                            <?php if ($remainingCodes > 0): ?>
                                You have <?= $remainingCodes ?> unused backup codes remaining
                            <?php else: ?>
                                You have no backup codes. Generate new ones to ensure account recovery access.
                            <?php endif; ?>
                        </p>
                    </div>
                    <div class="text-right">
                        <div class="text-2xl font-bold <?= $remainingCodes > 5 ? 'text-green-400' : ($remainingCodes > 2 ? 'text-yellow-400' : 'text-red-400') ?>">
                            <?= $remainingCodes ?>
                        </div>
                        <div class="text-xs text-gray-500">codes left</div>
                    </div>
                </div>

                <?php if ($remainingCodes <= 2): ?>
                    <div class="mt-4 p-4 bg-yellow-500/20 border border-yellow-500/50 rounded-lg">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-yellow-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            <span class="text-yellow-200 font-medium">Low backup codes warning</span>
                        </div>
                        <p class="text-yellow-300 text-sm mt-1">
                            You're running low on backup codes. Generate new ones to ensure you can always access your account.
                        </p>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Generate New Codes -->
            <div class="bg-gray-800/50 backdrop-blur-sm border border-gray-700 rounded-xl p-6">
                <h2 class="text-xl font-semibold text-white mb-4">Generate New Backup Codes</h2>
                
                <div class="mb-6">
                    <p class="text-gray-300 mb-4">
                        Backup codes are single-use codes that allow you to access your account if you can't receive email verification codes.
                    </p>
                    
                    <div class="bg-blue-500/20 border border-blue-500/50 rounded-lg p-4">
                        <h3 class="text-blue-200 font-medium mb-2">What happens when you generate new codes:</h3>
                        <ul class="text-blue-300 text-sm space-y-1">
                            <li>• All existing backup codes will be invalidated</li>
                            <li>• 10 new backup codes will be generated</li>
                            <li>• You'll need to save the new codes securely</li>
                            <li>• Each code can only be used once</li>
                        </ul>
                    </div>
                </div>

                <form method="POST" onsubmit="return confirm('This will invalidate all existing backup codes. Are you sure you want to continue?')">
                    <input type="hidden" name="action" value="generate_backup_codes">
                    <button type="submit" 
                            class="bg-salon-gold hover:bg-yellow-500 text-black font-medium px-6 py-3 rounded-lg transition-colors">
                        Generate New Backup Codes
                    </button>
                </form>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
function selectText(element) {
    if (window.getSelection) {
        const selection = window.getSelection();
        const range = document.createRange();
        range.selectNodeContents(element);
        selection.removeAllRanges();
        selection.addRange(range);
    }
}

function printCodes() {
    // Show print-only elements
    const printElements = document.querySelectorAll('.print-only');
    printElements.forEach(el => el.style.display = 'block');
    
    window.print();
    
    // Hide print-only elements after printing
    setTimeout(() => {
        printElements.forEach(el => el.style.display = 'none');
    }, 1000);
}
</script>

<?php include __DIR__ . '/../../includes/footer.php'; ?>
