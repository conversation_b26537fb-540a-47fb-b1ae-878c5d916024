<?php
/**
 * Admin 2FA Backup Codes Management
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/admin_2fa_functions.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    redirect('/admin/login.php');
}

$adminId = $_SESSION['user_id'];
$message = '';
$messageType = '';
$showCodes = false;
$backupCodes = [];

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $action = $_POST['action'] ?? '';

        // Set 2FA verification flag to prevent logout during operations
        $_SESSION['is_2fa_verified'] = true;

        switch ($action) {
            case 'generate_backup_codes':
                // Generate new backup codes
                $newCodes = generateBackupCodes(10);
                $result = storeAdminBackupCodes($adminId, $newCodes);

                if ($result['success']) {
                    $backupCodes = $newCodes;
                    $showCodes = true;
                    $message = 'New backup codes generated successfully. Please save them in a secure location.';
                    $messageType = 'success';
                } else {
                    throw new Exception($result['error']);
                }
                break;

            case 'download_codes':
                if (isset($_SESSION['backup_codes_download'])) {
                    $codes = $_SESSION['backup_codes_download'];
                    unset($_SESSION['backup_codes_download']);

                    header('Content-Type: text/plain');
                    header('Content-Disposition: attachment; filename="flix-backup-codes-' . date('Y-m-d') . '.txt"');

                    echo "Flix Salon & SPA - 2FA Backup Codes\n";
                    echo "Generated: " . date('Y-m-d H:i:s') . "\n";
                    echo "Admin: " . $_SESSION['user_name'] . "\n";
                    echo str_repeat("=", 50) . "\n\n";
                    echo "IMPORTANT: Save these codes in a secure location.\n";
                    echo "Each code can only be used once.\n\n";

                    foreach ($codes as $i => $code) {
                        echo ($i + 1) . ". " . $code . "\n";
                    }

                    echo "\n" . str_repeat("=", 50) . "\n";
                    echo "Keep these codes secure and accessible only to you.\n";
                    exit;
                }
                break;
        }
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
        error_log("Backup codes error for admin $adminId: " . $e->getMessage());
    }
}

// Get current 2FA settings
$twoFASettings = getAdmin2FASettings($adminId);
$remainingCodes = getRemainingBackupCodesCount($adminId);

// Store codes for download if they were just generated
if ($showCodes && !empty($backupCodes)) {
    $_SESSION['backup_codes_download'] = $backupCodes;
}

include __DIR__ . '/../../includes/admin_header.php';
?>

<div class="min-h-screen bg-gray-900 py-6">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-white">2FA Backup Codes</h1>
                    <p class="mt-2 text-gray-400">Manage your two-factor authentication backup codes</p>
                </div>
                <a href="/flix/admin/profile/index.php" class="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors">
                    ← Back to Profile
                </a>
            </div>
        </div>

        <!-- Messages -->
        <?php if ($message): ?>
            <div class="mb-6 p-4 rounded-lg <?php echo $messageType === 'success' ? 'bg-green-500/20 border border-green-500/50 text-green-200' : 'bg-red-500/20 border border-red-500/50 text-red-200'; ?>">
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <!-- Current Status -->
        <div class="bg-gray-800 rounded-lg p-6 mb-6">
            <h2 class="text-xl font-semibold text-white mb-4">Current Status</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="bg-gray-700 rounded-lg p-4">
                    <div class="text-sm text-gray-400">2FA Status</div>
                    <div class="text-lg font-semibold <?php echo $twoFASettings['is_enabled'] ? 'text-green-400' : 'text-red-400'; ?>">
                        <?php echo $twoFASettings['is_enabled'] ? 'Enabled' : 'Disabled'; ?>
                    </div>
                </div>
                <div class="bg-gray-700 rounded-lg p-4">
                    <div class="text-sm text-gray-400">Backup Codes</div>
                    <div class="text-lg font-semibold <?php echo $twoFASettings['backup_codes_enabled'] ? 'text-green-400' : 'text-gray-400'; ?>">
                        <?php echo $twoFASettings['backup_codes_enabled'] ? 'Generated' : 'Not Generated'; ?>
                    </div>
                </div>
                <div class="bg-gray-700 rounded-lg p-4">
                    <div class="text-sm text-gray-400">Remaining Codes</div>
                    <div class="text-lg font-semibold text-white">
                        <?php echo $remainingCodes; ?> / 10
                    </div>
                </div>
            </div>
        </div>

        <!-- Backup Codes Display -->
        <?php if ($showCodes && !empty($backupCodes)): ?>
            <div class="bg-yellow-500/20 border border-yellow-500/50 rounded-lg p-6 mb-6">
                <div class="flex items-start mb-4">
                    <svg class="w-6 h-6 text-yellow-400 mr-3 mt-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                    <div>
                        <h3 class="text-yellow-200 font-semibold text-lg">Your New Backup Codes</h3>
                        <p class="text-yellow-300 text-sm mt-1">
                            Save these codes in a secure location. Each code can only be used once and won't be shown again.
                        </p>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-3 mb-4">
                    <?php foreach ($backupCodes as $i => $code): ?>
                        <div class="bg-gray-800 rounded p-3 font-mono text-center">
                            <span class="text-gray-400 text-sm"><?php echo $i + 1; ?>.</span>
                            <span class="text-white font-semibold ml-2"><?php echo htmlspecialchars($code); ?></span>
                        </div>
                    <?php endforeach; ?>
                </div>

                <div class="flex flex-col sm:flex-row gap-3">
                    <form method="POST" class="inline">
                        <input type="hidden" name="action" value="download_codes">
                        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            Download as Text File
                        </button>
                    </form>
                    <button onclick="copyAllCodes()" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors">
                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                        </svg>
                        Copy All Codes
                    </button>
                </div>
            </div>
        <?php endif; ?>

        <!-- Generate New Codes -->
        <div class="bg-gray-800 rounded-lg p-6">
            <h2 class="text-xl font-semibold text-white mb-4">Generate Backup Codes</h2>

            <?php if ($twoFASettings['backup_codes_enabled']): ?>
                <div class="bg-red-500/20 border border-red-500/50 rounded-lg p-4 mb-4">
                    <div class="flex items-start">
                        <svg class="w-6 h-6 text-red-400 mr-3 mt-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                        <div>
                            <h4 class="text-red-200 font-semibold mb-2">Warning</h4>
                            <p class="text-red-300 text-sm">
                                Generating new backup codes will invalidate all existing codes. Make sure to save the new codes securely.
                            </p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <form method="POST" onsubmit="return confirmGeneration()">
                <input type="hidden" name="action" value="generate_backup_codes">
                <button type="submit" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg transition-colors font-semibold">
                    <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                    </svg>
                    Generate New Backup Codes
                </button>
            </form>

            <div class="mt-4 text-sm text-gray-400">
                <p>• Backup codes allow you to access your account if you lose your primary 2FA device</p>
                <p>• Each code can only be used once</p>
                <p>• Store them in a secure location separate from your primary device</p>
                <p>• You'll get 10 new codes that replace any existing ones</p>
            </div>
        </div>
    </div>
</div>

<script>
function confirmGeneration() {
    <?php if ($twoFASettings['backup_codes_enabled']): ?>
    return confirm('This will invalidate all existing backup codes. Are you sure you want to generate new ones?');
    <?php else: ?>
    return confirm('Generate 10 new backup codes for your account?');
    <?php endif; ?>
}

function copyAllCodes() {
    const codes = <?php echo json_encode($backupCodes ?? []); ?>;
    const text = codes.map((code, index) => `${index + 1}. ${code}`).join('\n');

    navigator.clipboard.writeText(text).then(() => {
        alert('All backup codes copied to clipboard!');
    }).catch(() => {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        alert('All backup codes copied to clipboard!');
    });
}
</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>