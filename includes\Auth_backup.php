<?php
/**
 * Authentication System
 * Flix Salonce - PHP Version
 */

// Ensure database is available
if (!isset($database)) {
    require_once __DIR__ . '/../config/database.php';
}

class Auth {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    /**
     * Register a new user
     */
    public function register($data) {
        try {
            // Validate input
            $errors = $this->validateRegistration($data);
            if (!empty($errors)) {
                return ['success' => false, 'errors' => $errors];
            }
            
            // Check if user already exists
            $existingUser = $this->db->fetch(
                "SELECT id FROM users WHERE email = ?",
                [$data['email']]
            );
            
            if ($existingUser) {
                return ['success' => false, 'errors' => ['email' => 'Email already exists']];
            }
            
            // Generate referral code
            $referralCode = $this->generateReferralCode($data['name']);
            
            // Hash password
            $hashedPassword = password_hash($data['password'], PASSWORD_BCRYPT, ['cost' => BCRYPT_COST]);
            
            // Handle referral
            $referrerId = null;
            $bonusPoints = 0;
            if (!empty($data['referral_code'])) {
                $referrer = $this->db->fetch(
                    "SELECT id FROM users WHERE referral_code = ?",
                    [$data['referral_code']]
                );
                if ($referrer) {
                    $referrerId = $referrer['id'];
                    $bonusPoints = 100; // Bonus points for being referred
                }
            }
            
            // Insert user
            $userId = generateUUID();
            $this->db->query(
                "INSERT INTO users (id, name, email, password, phone, referral_code, referred_by, points)
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
                [
                    $userId,
                    $data['name'],
                    $data['email'],
                    $hashedPassword,
                    $data['phone'] ?? null,
                    $referralCode,
                    $referrerId,
                    $bonusPoints
                ]
            );
            
            // Award referral points to referrer
            if ($referrerId) {
                $this->db->query(
                    "UPDATE users SET points = points + 200 WHERE id = ?",
                    [$referrerId]
                );
                
                // Log point transaction for referrer
                $this->logPointTransaction($referrerId, 200, 'BONUS', 'Referral bonus');
            }
            
            // Log point transaction for new user
            if ($bonusPoints > 0) {
                $this->logPointTransaction($userId, $bonusPoints, 'BONUS', 'Welcome bonus');
            }

            // Try to create notification for new customer registration (non-blocking)
            try {
                require_once __DIR__ . '/notification_triggers.php';
                if (function_exists('createCustomerNotification')) {
                    createCustomerNotification($userId, 'CUSTOMER_NEW');
                }
            } catch (Exception $e) {
                error_log("Failed to create customer notification: " . $e->getMessage());
            }

            // Try to send welcome email to new customer (non-blocking)
            try {
                require_once __DIR__ . '/email_functions.php';
                if (function_exists('sendWelcomeEmail')) {
                    sendWelcomeEmail($userId);
                }
            } catch (Exception $e) {
                error_log("Failed to send welcome email: " . $e->getMessage());
            }

            return [
                'success' => true,
                'user_id' => $userId,
                'referral_code' => $referralCode,
                'bonus_points' => $bonusPoints
            ];
            
        } catch (Exception $e) {
            error_log("Registration failed with exception: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());
            return ['success' => false, 'errors' => ['general' => 'Registration failed: ' . $e->getMessage()]];
        }
    }
    
    /**
     * Login user
     */
    public function login($email, $password) {
        try {
            $user = $this->db->fetch(
                "SELECT * FROM users WHERE email = ?",
                [$email]
            );

            if (!$user || !password_verify($password, $user['password'])) {
                return ['success' => false, 'error' => 'Invalid credentials'];
            }

            // Check if admin user has 2FA enabled
            if ($user['role'] === 'ADMIN') {
                try {
                    require_once __DIR__ . '/admin_2fa_functions.php';

                    if (isAdmin2FAEnabled($user['id'], $this->db)) {
                        // Create temporary session for 2FA verification
                        $this->create2FASession($user);

                        return [
                            'success' => true,
                            'requires_2fa' => true,
                            'user' => [
                                'id' => $user['id'],
                                'name' => $user['name'],
                                'email' => $user['email'],
                                'role' => $user['role']
                            ]
                        ];
                    }
                } catch (Exception $e) {
                    error_log("2FA check failed for admin {$user['id']}: " . $e->getMessage());
                    // Continue with regular login if 2FA check fails
                }
            }

            // Create regular session for non-2FA users
            $this->createSession($user);

            return [
                'success' => true,
                'requires_2fa' => false,
                'user' => [
                    'id' => $user['id'],
                    'name' => $user['name'],
                    'email' => $user['email'],
                    'role' => $user['role']
                ]
            ];

        } catch (Exception $e) {
            return ['success' => false, 'error' => 'Login failed'];
        }
    }
    
    /**
     * Logout user
     */
    public function logout() {
        if (isset($_SESSION['session_token'])) {
            // Remove session from database
            $this->db->query(
                "DELETE FROM sessions WHERE session_token = ?",
                [$_SESSION['session_token']]
            );
        }

        // Clear all session data
        $_SESSION = array();

        // Delete the session cookie with proper parameters
        if (ini_get("session.use_cookies")) {
            // Detect HTTPS
            $isHttps = (
                (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') ||
                (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https') ||
                (isset($_SERVER['HTTP_X_FORWARDED_SSL']) && $_SERVER['HTTP_X_FORWARDED_SSL'] === 'on') ||
                (isset($_SERVER['SERVER_PORT']) && $_SERVER['SERVER_PORT'] == 443)
            );

            // Clear session cookie with same parameters used to set it
            setcookie(
                session_name(),
                '',
                [
                    'expires' => time() - 86400, // 24 hours ago to ensure deletion
                    'path' => '/',
                    'domain' => '',
                    'secure' => $isHttps,
                    'httponly' => true,
                    'samesite' => 'Lax'
                ]
            );
        }

        // Destroy session
        session_destroy();
        return true;
    }
    
    /**
     * Check if user is authenticated
     */
    public function isAuthenticated() {
        // Check if session variables exist
        if (!isset($_SESSION['user_id']) || !isset($_SESSION['session_token'])) {
            if (!isProduction()) {
                error_log("Session authentication failed: Missing session variables");
            }
            return false;
        }

        try {
            // Verify session in database
            $session = $this->db->fetch(
                "SELECT * FROM sessions WHERE session_token = ? AND expires > NOW()",
                [$_SESSION['session_token']]
            );

            if (!$session) {
                if (!isProduction()) {
                    error_log("Session authentication failed: Session not found in database or expired for token: " . substr($_SESSION['session_token'], 0, 8) . "...");
                }
                // Clear invalid session data
                $this->clearSessionData();
                return false;
            }

            // Verify user still exists and is active
            $user = $this->db->fetch(
                "SELECT id, role FROM users WHERE id = ?",
                [$_SESSION['user_id']]
            );

            if (!$user) {
                if (!isProduction()) {
                    error_log("Session authentication failed: User not found for ID: " . $_SESSION['user_id']);
                }
                // Clear invalid session data
                $this->clearSessionData();
                return false;
            }

            // Check for 2FA requirements for admin users
            if ($user['role'] === 'ADMIN') {
                // Check if this is a 2FA pending session
                if (isset($_SESSION['requires_2fa']) && $_SESSION['requires_2fa']) {
                    // Check if 2FA session has expired (15 minutes)
                    if (isset($_SESSION['2fa_expires']) && time() > $_SESSION['2fa_expires']) {
                        $this->clearSessionData();
                        return false;
                    }

                    // If 2FA is not yet verified, don't consider fully authenticated
                    if (!isset($_SESSION['is_2fa_verified']) || !$_SESSION['is_2fa_verified']) {
                        return false;
                    }
                }

                // Check if admin has 2FA enabled but session doesn't have 2FA verification
                require_once __DIR__ . '/admin_2fa_functions.php';
                if (isAdmin2FAEnabled($user['id'])) {
                    // If 2FA is enabled but session doesn't show 2FA verification, require re-authentication
                    if (!isset($_SESSION['is_2fa_verified']) || !$_SESSION['is_2fa_verified']) {
                        // Don't logout during 2FA setup operations
                        if (!$this->is2FASetupOperation()) {
                            $this->clearSessionData();
                            return false;
                        }
                    }
                }
            }

            // Update last activity timestamp
            $_SESSION['last_activity'] = time();

            // Update session expiration in database (sliding expiration)
            // Only update every 5 minutes to reduce database load
            $lastUpdate = $_SESSION['last_session_update'] ?? 0;
            if (time() - $lastUpdate > 300) { // 5 minutes
                $newExpires = date('Y-m-d H:i:s', strtotime('+2 weeks'));
                $this->db->query(
                    "UPDATE sessions SET expires = ? WHERE session_token = ?",
                    [$newExpires, $_SESSION['session_token']]
                );
                $_SESSION['last_session_update'] = time();
            }

            return true;

        } catch (Exception $e) {
            if (!isProduction()) {
                error_log("Session authentication error: " . $e->getMessage());
            }
            return false;
        }
    }

    /**
     * Check if current request is a 2FA setup/management operation
     */
    private function is2FASetupOperation() {
        // Check if we're in admin profile or 2FA management pages
        $currentScript = $_SERVER['SCRIPT_NAME'] ?? '';
        $currentAction = $_POST['action'] ?? $_GET['action'] ?? '';

        // Allow 2FA operations in admin profile
        if (strpos($currentScript, '/admin/profile/') !== false) {
            return true;
        }

        // Allow specific 2FA actions
        $allowedActions = [
            'generate_backup_codes',
            'enable_2fa',
            'disable_2fa',
            'verify_2fa_code',
            'setup_2fa'
        ];

        return in_array($currentAction, $allowedActions);
    }

    /**
     * Get current user
     */
    public function getCurrentUser() {
        if (!$this->isAuthenticated()) {
            return null;
        }
        
        return $this->db->fetch(
            "SELECT * FROM users WHERE id = ?",
            [$_SESSION['user_id']]
        );
    }

    /**
     * Get current user ID
     */
    public function getUserId() {
        if (!$this->isAuthenticated()) {
            return null;
        }
        return $_SESSION['user_id'] ?? null;
    }
    
    /**
     * Check if user has specific role
     */
    public function hasRole($role) {
        $user = $this->getCurrentUser();
        return $user && $user['role'] === $role;
    }
    
    /**
     * Require authentication
     */
    public function requireAuth() {
        if (!$this->isAuthenticated()) {
            redirect('/auth/login.php');
        }
    }
    
    /**
     * Require specific role
     */
    public function requireRole($role) {
        $this->requireAuth();
        if (!$this->hasRole($role)) {
            header('HTTP/1.1 403 Forbidden');
            exit('Access denied');
        }
    }

    /**
     * Create 2FA session for admin users
     */
    private function create2FASession($user) {
        // Regenerate session ID for security
        session_regenerate_id(true);

        // Set temporary session data for 2FA verification
        $_SESSION['requires_2fa'] = true;
        $_SESSION['is_2fa_verified'] = false;
        $_SESSION['pending_2fa_admin_id'] = $user['id'];
        $_SESSION['pending_2fa_user_data'] = [
            'id' => $user['id'],
            'name' => $user['name'],
            'email' => $user['email'],
            'role' => $user['role']
        ];
        $_SESSION['2fa_login_time'] = time();

        // Set a shorter session timeout for 2FA (15 minutes)
        $_SESSION['2fa_expires'] = time() + 900;
    }

    /**
     * Complete 2FA verification and create full session
     */
    public function complete2FAVerification() {
        // Verify we're in a 2FA pending state
        if (!isset($_SESSION['requires_2fa']) || !$_SESSION['requires_2fa']) {
            return ['success' => false, 'error' => 'No 2FA verification pending'];
        }

        if (!isset($_SESSION['pending_2fa_admin_id'])) {
            return ['success' => false, 'error' => 'Invalid 2FA session'];
        }

        // Check if 2FA session has expired
        if (isset($_SESSION['2fa_expires']) && time() > $_SESSION['2fa_expires']) {
            $this->clearSessionData();
            return ['success' => false, 'error' => '2FA session expired. Please log in again.'];
        }

        // Get the pending user data
        $userData = $_SESSION['pending_2fa_user_data'];

        // Create full session
        $this->createSession($userData);

        // Mark session as 2FA verified
        $_SESSION['is_2fa_verified'] = true;

        // Clean up 2FA session data
        unset($_SESSION['requires_2fa']);
        unset($_SESSION['pending_2fa_admin_id']);
        unset($_SESSION['pending_2fa_user_data']);
        unset($_SESSION['2fa_login_time']);
        unset($_SESSION['2fa_expires']);

        return [
            'success' => true,
            'user' => [
                'id' => $userData['id'],
                'name' => $userData['name'],
                'email' => $userData['email'],
                'role' => $userData['role']
            ]
        ];
    }

    /**
     * Create user session
     */
    private function createSession($user) {
        // Regenerate session ID for security (prevent session fixation)
        session_regenerate_id(true);

        $sessionId = generateUUID();
        $sessionToken = bin2hex(random_bytes(32));
        $expires = date('Y-m-d H:i:s', strtotime('+2 weeks')); // Changed to 2 weeks

        // Store session in database
        $this->db->query(
            "INSERT INTO sessions (id, session_token, user_id, expires) VALUES (?, ?, ?, ?)",
            [$sessionId, $sessionToken, $user['id'], $expires]
        );

        // Set session variables
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_role'] = $user['role'];
        $_SESSION['user_name'] = $user['name'];
        $_SESSION['session_token'] = $sessionToken;
        $_SESSION['last_activity'] = time();
        $_SESSION['last_session_update'] = time();

        // Force persistent session cookie (critical for production)
        $this->setPersistentSessionCookie();

        // Debug: Log session creation
        if (function_exists('isProduction') && !isProduction()) {
            error_log("Session created for user: " . $user['email'] . " - Session ID: " . session_id() . " - Token: " . substr($sessionToken, 0, 8) . "...");
        }

        // Transfer session wishlist to database
        $this->transferSessionWishlistToDatabase($user['id']);
    }

    /**
     * Set persistent session cookie
     */
    private function setPersistentSessionCookie() {
        // Detect HTTPS
        $isHttps = (
            (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') ||
            (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https') ||
            (isset($_SERVER['HTTP_X_FORWARDED_SSL']) && $_SERVER['HTTP_X_FORWARDED_SSL'] === 'on') ||
            (isset($_SERVER['SERVER_PORT']) && $_SERVER['SERVER_PORT'] == 443)
        );

        $sessionLifetime = 1209600; // 2 weeks

        // Set persistent session cookie with explicit domain
        $domain = '';
        if (function_exists('isProduction') && isProduction()) {
            // Leave domain empty for production to avoid subdomain issues
            $domain = '';
        }
        
        setcookie(
            session_name(),
            session_id(),
            [
                'expires' => time() + $sessionLifetime,
                'path' => '/',
                'domain' => $domain,
                'secure' => $isHttps,
                'httponly' => true,
                'samesite' => 'Lax'
            ]
        );

        if (!isProduction()) {
            error_log("Persistent session cookie set - HTTPS: " . ($isHttps ? 'YES' : 'NO') . " - Expires: " . date('Y-m-d H:i:s', time() + $sessionLifetime));
        }
    }

    /**
     * Refresh session cookie to maintain persistence
     */
    public function refreshSessionCookie() {
        // Only refresh if user is authenticated and cookie needs refreshing
        if (!$this->isAuthenticated()) {
            return false;
        }

        // Check if cookie needs refreshing (every hour)
        $lastRefresh = $_SESSION['last_cookie_refresh'] ?? 0;
        if (time() - $lastRefresh < 3600) { // 1 hour
            return false;
        }

        $this->setPersistentSessionCookie();
        $_SESSION['last_cookie_refresh'] = time();

        return true;
    }

    /**
     * Clear session data without destroying the session
     */
    private function clearSessionData() {
        unset($_SESSION['user_id']);
        unset($_SESSION['user_role']);
        unset($_SESSION['user_name']);
        unset($_SESSION['session_token']);
        unset($_SESSION['last_activity']);
        unset($_SESSION['last_session_update']);
        unset($_SESSION['last_cookie_refresh']);
    }

    /**
     * Transfer session wishlist to database (when user logs in)
     */
    private function transferSessionWishlistToDatabase($userId) {
        if (!isset($_SESSION['wishlist']) || empty($_SESSION['wishlist'])) {
            return;
        }

        foreach ($_SESSION['wishlist'] as $key) {
            list($itemType, $itemId) = explode('_', $key, 2);

            // Check if item already exists in database wishlist
            $existing = $this->db->fetch(
                "SELECT id FROM wishlists WHERE user_id = ? AND item_type = ? AND item_id = ?",
                [$userId, $itemType, $itemId]
            );

            if (!$existing) {
                // Add to database wishlist
                $wishlistId = generateUUID();
                $this->db->query(
                    "INSERT INTO wishlists (id, user_id, item_type, item_id) VALUES (?, ?, ?, ?)",
                    [$wishlistId, $userId, $itemType, $itemId]
                );
            }
        }

        // Clear session wishlist
        unset($_SESSION['wishlist']);
    }

    /**
     * Clean up expired sessions from database
     */
    public function cleanupExpiredSessions() {
        try {
            $this->db->query("DELETE FROM sessions WHERE expires < NOW()");
            return true;
        } catch (Exception $e) {
            error_log("Session cleanup error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get session information for debugging
     */
    public function getSessionInfo() {
        if (!$this->isAuthenticated()) {
            return null;
        }

        return [
            'user_id' => $_SESSION['user_id'] ?? null,
            'user_role' => $_SESSION['user_role'] ?? null,
            'user_name' => $_SESSION['user_name'] ?? null,
            'session_token' => isset($_SESSION['session_token']) ? 'Set' : 'Not set',
            'last_activity' => $_SESSION['last_activity'] ?? null,
            'session_id' => session_id(),
            'cookie_params' => session_get_cookie_params()
        ];
    }

    /**
     * Validate registration data
     */
    private function validateRegistration($data) {
        $errors = [];
        
        if (empty($data['name']) || strlen($data['name']) < 2) {
            $errors['name'] = 'Name must be at least 2 characters';
        }
        
        if (empty($data['email']) || !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $errors['email'] = 'Valid email is required';
        }
        
        if (empty($data['password']) || strlen($data['password']) < 8) {
            $errors['password'] = 'Password must be at least 8 characters';
        }
        
        return $errors;
    }
    
    /**
     * Generate unique referral code
     */
    private function generateReferralCode($name) {
        $cleanName = preg_replace('/[^a-zA-Z]/', '', strtoupper($name));
        $prefix = substr($cleanName, 0, 3);
        $suffix = str_pad(mt_rand(0, 999), 3, '0', STR_PAD_LEFT);
        
        $code = $prefix . $suffix;
        
        // Ensure uniqueness
        $existing = $this->db->fetch(
            "SELECT id FROM users WHERE referral_code = ?",
            [$code]
        );
        
        if ($existing) {
            return $this->generateReferralCode($name . mt_rand(1, 99));
        }
        
        return $code;
    }
    
    /**
     * Log point transaction
     */
    private function logPointTransaction($userId, $points, $type, $description) {
        $transactionId = generateUUID();
        $this->db->query(
            "INSERT INTO point_transactions (id, user_id, points, type, description) VALUES (?, ?, ?, ?, ?)",
            [$transactionId, $userId, $points, $type, $description]
        );
    }
}

// Global auth instance
if (isset($database)) {
    $auth = new Auth($database);

    // Ensure session persistence on every request for authenticated users
    if (isset($_SESSION['user_id']) && isset($_SESSION['session_token'])) {
        // Refresh session cookie to maintain persistence
        $auth->refreshSessionCookie();
    }

    // Periodically cleanup expired sessions (run randomly to avoid performance impact)
    // Reduced frequency from 5% to 1% to be less aggressive
    if (mt_rand(1, 100) <= 1) { // 1% chance to run cleanup
        $auth->cleanupExpiredSessions();
    }
}
