# Two-Factor Authentication (2FA) Implementation Guide

## Overview

This document provides comprehensive documentation for the Two-Factor Authentication (2FA) system implemented for the Flix Salon admin panel. The system enhances security by requiring a second form of authentication beyond username and password.

## Features

- **Email-based 2FA codes**: Secure verification codes sent via email
- **Backup codes**: Emergency access codes for account recovery
- **Admin management**: Complete 2FA settings management interface
- **SMTP integration**: Professional email delivery using existing email service
- **Security logging**: Comprehensive audit trail for 2FA activities
- **Mobile-responsive UI**: Modern interface matching admin panel design

## System Requirements

- PHP 7.4 or higher
- MySQL/MariaDB database
- PHPMailer library (already included)
- SMTP email service configured
- Modern web browser with JavaScript enabled

## Database Schema

### New Tables Created

#### `admin_2fa_settings`
```sql
CREATE TABLE admin_2fa_settings (
    id VARCHAR(36) PRIMARY KEY,
    admin_id VARCHAR(36) NOT NULL UNIQUE,
    is_enabled TINYINT(1) DEFAULT 0,
    email_2fa_enabled TINYINT(1) DEFAULT 0,
    backup_codes_enabled TINYINT(1) DEFAULT 0,
    backup_codes_generated_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_admin_2fa_admin_id (admin_id),
    FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE
);
```

#### `admin_2fa_email_codes`
```sql
CREATE TABLE admin_2fa_email_codes (
    id VARCHAR(36) PRIMARY KEY,
    admin_id VARCHAR(36) NOT NULL,
    code VARCHAR(6) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    is_used TINYINT(1) DEFAULT 0,
    attempts INT DEFAULT 0,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_admin_2fa_email_admin_id (admin_id),
    INDEX idx_admin_2fa_email_expires (expires_at),
    INDEX idx_admin_2fa_email_code (code),
    FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE
);
```

#### `admin_2fa_backup_codes`
```sql
CREATE TABLE admin_2fa_backup_codes (
    id VARCHAR(36) PRIMARY KEY,
    admin_id VARCHAR(36) NOT NULL,
    code_hash VARCHAR(255) NOT NULL,
    is_used TINYINT(1) DEFAULT 0,
    used_at TIMESTAMP NULL,
    used_ip VARCHAR(45) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_admin_2fa_backup_admin_id (admin_id),
    INDEX idx_admin_2fa_backup_used (is_used),
    FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE
);
```

## File Structure

### Core Files

#### `/includes/admin_2fa_functions.php`
- Core 2FA functionality
- Email sending with SMTP integration
- Backup codes generation and validation
- Security utilities and helpers

#### `/admin/auth/backup-codes.php`
- Backup codes management interface
- Generate, download, and view backup codes
- Matches admin panel styling and layout

#### `/admin/profile/index.php` (Modified)
- Added 2FA settings section
- Toggle 2FA enable/disable
- Backup codes management link
- Security status display

### Authentication Files

#### `/admin/login.php` (Modified)
- Enhanced login flow with 2FA verification
- Email code validation
- Backup code authentication
- Session management improvements

#### `/admin/auth/verify-2fa.php`
- 2FA code verification interface
- Email code and backup code input
- Resend code functionality
- Security validation

### Utility Files

#### `/admin/test_2fa_email.php`
- Email testing utility for debugging
- SMTP configuration validation
- Template testing interface

## Implementation Details

### Email Integration

The system uses the existing SMTP email service configured in the application:

```php
// Email configuration (uses existing settings)
$mail = new PHPMailer(true);
$mail->isSMTP();
$mail->Host = 'smtp.gmail.com';
$mail->SMTPAuth = true;
$mail->Username = '<EMAIL>';
$mail->Password = 'your-app-password';
$mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
$mail->Port = 587;
```

### Security Features

1. **Code Expiration**: Email codes expire after 10 minutes
2. **Rate Limiting**: Prevents brute force attacks
3. **Secure Generation**: Cryptographically secure random codes
4. **Audit Logging**: All 2FA activities are logged
5. **Session Protection**: Enhanced session security
6. **Backup Codes**: Emergency access when primary method fails

### User Interface

The 2FA interface follows the admin panel design patterns:
- Consistent styling with `bg-secondary-800` and `bg-salon-black`
- Responsive grid layout with sidebar navigation
- Professional color scheme with salon gold accents
- Mobile-friendly responsive design

## Installation Guide

### Step 1: Database Setup

**IMPORTANT**: This system uses the existing `users` table with role-based access. Admin users have `role = 'ADMIN'` in the `users` table.

**For Local Development:**
```bash
mysql -u root -p flix_salonce < database/create_2fa_tables.sql
```

**For Live Server Deployment:**
```bash
mysql -u your_username -p your_database_name < database/deploy_2fa_live_server.sql
```

**Note**: The live server SQL file (`deploy_2fa_live_server.sql`) has been corrected to reference the `users` table instead of a non-existent `admins` table.

**Manual SQL Commands** (if needed):

```sql
-- Create 2FA settings table
CREATE TABLE admin_2fa_settings (
    id VARCHAR(36) PRIMARY KEY,
    admin_id VARCHAR(36) NOT NULL UNIQUE,
    is_enabled TINYINT(1) DEFAULT 0,
    email_2fa_enabled TINYINT(1) DEFAULT 0,
    backup_codes_enabled TINYINT(1) DEFAULT 0,
    backup_codes_generated_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_admin_2fa_admin_id (admin_id),
    FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create email verification codes table
CREATE TABLE admin_2fa_email_codes (
    id VARCHAR(36) PRIMARY KEY,
    admin_id VARCHAR(36) NOT NULL,
    code VARCHAR(6) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    is_used TINYINT(1) DEFAULT 0,
    attempts INT DEFAULT 0,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_admin_2fa_email_admin_id (admin_id),
    INDEX idx_admin_2fa_email_expires (expires_at),
    INDEX idx_admin_2fa_email_code (code),
    FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create backup codes table
CREATE TABLE admin_2fa_backup_codes (
    id VARCHAR(36) PRIMARY KEY,
    admin_id VARCHAR(36) NOT NULL,
    code_hash VARCHAR(255) NOT NULL,
    is_used TINYINT(1) DEFAULT 0,
    used_at TIMESTAMP NULL,
    used_ip VARCHAR(45) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_admin_2fa_backup_admin_id (admin_id),
    INDEX idx_admin_2fa_backup_used (is_used),
    FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create 2FA attempt rate limiting table
CREATE TABLE admin_2fa_attempts (
    id VARCHAR(36) PRIMARY KEY,
    admin_id VARCHAR(36) NULL,
    ip_address VARCHAR(45) NOT NULL,
    attempt_type ENUM('EMAIL_CODE', 'BACKUP_CODE') NOT NULL,
    is_successful TINYINT(1) DEFAULT 0,
    attempted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_admin_2fa_attempts_admin_id (admin_id),
    INDEX idx_admin_2fa_attempts_ip (ip_address),
    INDEX idx_admin_2fa_attempts_time (attempted_at),
    FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create 2FA audit logs table
CREATE TABLE admin_2fa_logs (
    id VARCHAR(36) PRIMARY KEY,
    admin_id VARCHAR(36) NOT NULL,
    action VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    metadata JSON NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_admin_2fa_logs_admin_id (admin_id),
    INDEX idx_admin_2fa_logs_action (action),
    INDEX idx_admin_2fa_logs_created_at (created_at),
    FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE
);
```

### Step 2: File Deployment

Ensure all the following files are properly deployed:

1. **Core Functions**: `/includes/admin_2fa_functions.php`
2. **Backup Codes Interface**: `/admin/auth/backup-codes.php`
3. **Verification Interface**: `/admin/auth/verify-2fa.php`
4. **Modified Login**: `/admin/login.php`
5. **Modified Profile**: `/admin/profile/index.php`

### Step 3: SMTP Configuration

Verify that your SMTP email service is properly configured in the application. The 2FA system uses the existing email configuration.

### Step 4: Testing

1. **Email Testing**: Use `/admin/test_2fa_email.php` to verify email delivery
2. **2FA Flow**: Test the complete 2FA enable/disable flow
3. **Backup Codes**: Generate and test backup code functionality
4. **Login Flow**: Test login with 2FA enabled

## Usage Instructions

### For Administrators

#### Enabling 2FA

1. Navigate to **Admin Profile** (`/admin/profile`)
2. Locate the **Two-Factor Authentication** section
3. Click **Enable 2FA** button
4. Follow the email verification process
5. Generate backup codes for emergency access

#### Managing Backup Codes

1. From the profile page, click **Manage Backup Codes**
2. Generate new codes when needed
3. Download codes as a text file
4. Store codes securely offline

#### Login with 2FA

1. Enter username and password normally
2. Check email for 6-digit verification code
3. Enter code on verification page
4. Use backup codes if email is unavailable

### For Developers

#### Customizing Email Templates

Email templates can be modified in `/includes/admin_2fa_functions.php`:

```php
function sendAdminEmail($to, $subject, $message, $isHTML = true) {
    // Customize email templates here
    // Add company branding, styling, etc.
}
```

#### Adding Additional Security Features

The system is designed to be extensible. Additional features can be added:

- TOTP/Authenticator app support
- SMS-based verification
- Hardware token support
- Advanced rate limiting

## Troubleshooting

### Common Issues

#### Email Not Received
1. Check SMTP configuration
2. Verify email address is correct
3. Check spam/junk folders
4. Use test email utility

#### Backup Codes Not Working
1. Ensure codes haven't been used
2. Check database connectivity
3. Verify admin ID matches

#### Login Loops
1. Clear browser cache and cookies
2. Check session configuration
3. Verify database tables exist

### Debug Mode

Enable debug mode by adding to the top of 2FA functions:

```php
// Add to admin_2fa_functions.php for debugging
ini_set('display_errors', 1);
error_reporting(E_ALL);
```

## Security Considerations

### Best Practices

1. **Regular Code Rotation**: Generate new backup codes periodically
2. **Secure Storage**: Store backup codes offline securely
3. **Monitor Logs**: Review 2FA activity logs regularly
4. **Email Security**: Use strong SMTP authentication
5. **Session Management**: Implement proper session timeouts

### Production Deployment

1. **Disable Debug Mode**: Remove all debug statements
2. **HTTPS Required**: Ensure SSL/TLS encryption
3. **Database Security**: Use strong database passwords
4. **Regular Updates**: Keep system components updated
5. **Backup Strategy**: Regular database backups

## Support and Maintenance

### Regular Maintenance Tasks

1. **Clean Expired Codes**: Remove old 2FA codes from database
2. **Monitor Email Delivery**: Check email service status
3. **Review Security Logs**: Analyze 2FA usage patterns
4. **Update Documentation**: Keep guides current

### Performance Optimization

1. **Database Indexing**: Add indexes for frequently queried fields
2. **Email Queue**: Implement email queuing for high volume
3. **Cache Settings**: Optimize session and cache configuration

## Conclusion

The 2FA system provides robust security enhancement for the admin panel while maintaining user-friendly operation. The implementation follows security best practices and integrates seamlessly with the existing application architecture.

For additional support or feature requests, refer to the development team or system administrator.

---

**Document Version**: 1.0  
**Last Updated**: 2025-07-07  
**Author**: Augment Agent  
**Status**: Production Ready
