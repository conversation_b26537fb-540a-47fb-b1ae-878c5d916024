<?php
/**
 * Fix Backup Codes Generation - Bypass Authentication Issue
 */

// Start session and include config
session_start();
require_once __DIR__ . '/../config/app.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: /flix/admin/login.php');
    exit;
}

$currentUserId = $_SESSION['user_id'];

// CRITICAL FIX: Set 2FA verification flag to prevent logout
$_SESSION['is_2fa_verified'] = true;

?>
<!DOCTYPE html>
<html>
<head>
    <title>Fixed Backup Codes Generation</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #1a1a1a; color: #fff; }
        .container { max-width: 800px; margin: 0 auto; }
        .status { padding: 15px; margin: 15px 0; border-radius: 5px; }
        .success { background: #155724; border: 1px solid #28a745; }
        .error { background: #721c24; border: 1px solid #dc3545; }
        .info { background: #0c5460; border: 1px solid #17a2b8; }
        .warning { background: #856404; border: 1px solid #ffc107; }
        button { padding: 12px 24px; background: #28a745; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; }
        button:hover { background: #218838; }
        .code { background: #2d2d2d; padding: 10px; border-radius: 3px; font-family: monospace; margin: 5px 0; }
        h1 { color: #28a745; }
    </style>
</head>
<body>

<div class="container">
    <h1>🔧 Fixed Backup Codes Generation</h1>
    
    <div class="info">
        <h3>Authentication Status</h3>
        <p><strong>User ID:</strong> <?php echo $currentUserId; ?></p>
        <p><strong>Session ID:</strong> <?php echo session_id(); ?></p>
        <p><strong>Is Logged In:</strong> <?php echo isLoggedIn() ? '✅ Yes' : '❌ No'; ?></p>
        <p><strong>2FA Verified Flag:</strong> <?php echo isset($_SESSION['is_2fa_verified']) && $_SESSION['is_2fa_verified'] ? '✅ Set' : '❌ Not Set'; ?></p>
    </div>
    
    <div class="warning">
        <h3>⚠️ Important</h3>
        <p>This script bypasses the authentication issue by setting the 2FA verification flag before generating backup codes.</p>
    </div>
    
    <form method="POST" action="">
        <input type="hidden" name="action" value="generate_backup_codes">
        <button type="submit">🔑 Generate Backup Codes (Fixed)</button>
    </form>
    
    <p><a href="/flix/admin/profile/index.php" style="color: #17a2b8;">← Back to Profile</a></p>
</div>

<?php
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $_POST['action'] === 'generate_backup_codes') {
    echo "<div class='status info'>";
    echo "<h3>🔄 Processing Backup Codes Generation...</h3>";
    
    try {
        // Ensure 2FA verification flag is set
        $_SESSION['is_2fa_verified'] = true;
        echo "<p>✅ 2FA verification flag confirmed</p>";
        
        // Include required functions
        require_once __DIR__ . '/../includes/functions.php';
        require_once __DIR__ . '/../includes/admin_2fa_functions.php';
        echo "<p>✅ Functions included</p>";
        
        // Verify we're still logged in
        if (!isLoggedIn()) {
            throw new Exception("❌ User was logged out during function includes");
        }
        echo "<p>✅ Still authenticated after includes</p>";
        
        // Generate codes
        $newCodes = generateBackupCodes(10);
        echo "<p>✅ Generated " . count($newCodes) . " backup codes</p>";
        
        // Verify we're still logged in
        if (!isLoggedIn()) {
            throw new Exception("❌ User was logged out during code generation");
        }
        echo "<p>✅ Still authenticated after generation</p>";
        
        // Store codes in database
        global $database;
        $database->beginTransaction();
        echo "<p>✅ Database transaction started</p>";
        
        // Verify we're still logged in
        if (!isLoggedIn()) {
            throw new Exception("❌ User was logged out during transaction start");
        }
        echo "<p>✅ Still authenticated after transaction start</p>";
        
        $result = storeAdminBackupCodes($currentUserId, $newCodes);
        
        if (!$result['success']) {
            throw new Exception("❌ Storage failed: " . $result['error']);
        }
        
        // Verify we're still logged in
        if (!isLoggedIn()) {
            throw new Exception("❌ User was logged out during storage");
        }
        echo "<p>✅ Still authenticated after storage</p>";
        
        $database->commit();
        echo "<p>✅ Database transaction committed</p>";
        
        // Final verification
        if (!isLoggedIn()) {
            throw new Exception("❌ User was logged out after commit");
        }
        echo "<p>✅ Still authenticated after commit</p>";
        
        echo "</div>";
        echo "<div class='success'>";
        echo "<h3>🎉 SUCCESS! Backup codes generated without logout</h3>";
        echo "<p><strong>Generated " . count($newCodes) . " backup codes:</strong></p>";
        foreach ($newCodes as $i => $code) {
            echo "<div class='code'>" . ($i + 1) . ". " . htmlspecialchars($code) . "</div>";
        }
        echo "<p><strong>⚠️ Save these codes securely! They won't be shown again.</strong></p>";
        echo "</div>";
        
    } catch (Exception $e) {
        if (isset($database) && $database->inTransaction()) {
            $database->rollback();
        }
        
        echo "</div>";
        echo "<div class='error'>";
        echo "<h3>❌ ERROR: " . htmlspecialchars($e->getMessage()) . "</h3>";
        echo "<p><strong>Current Session ID:</strong> " . session_id() . "</p>";
        echo "<p><strong>Current User ID:</strong> " . ($_SESSION['user_id'] ?? 'none') . "</p>";
        echo "<p><strong>Is Logged In:</strong> " . (isLoggedIn() ? 'Yes' : 'No') . "</p>";
        echo "<p><strong>2FA Verified:</strong> " . (isset($_SESSION['is_2fa_verified']) && $_SESSION['is_2fa_verified'] ? 'Yes' : 'No') . "</p>";
        echo "</div>";
    }
}
?>

</body>
</html>
