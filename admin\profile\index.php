<?php
require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/admin_profile_functions.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    redirect('/auth/login.php');
}

$currentUserId = $_SESSION['user_id'];
$message = '';
$messageType = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'update_profile':
                $data = [
                    'name' => trim($_POST['name']),
                    'email' => trim($_POST['email']),
                    'phone' => trim($_POST['phone']),
                    'date_of_birth' => $_POST['date_of_birth'] ?: null
                ];
                
                updateAdminProfile($currentUserId, $data);
                
                // Update session data
                $_SESSION['user_name'] = $data['name'];
                
                $message = 'Profile updated successfully!';
                $messageType = 'success';
                break;
                
            case 'change_password':
                $currentPassword = $_POST['current_password'];
                $newPassword = $_POST['new_password'];
                $confirmPassword = $_POST['confirm_password'];
                
                changeAdminPassword($currentUserId, $currentPassword, $newPassword, $confirmPassword);
                
                $message = 'Password changed successfully!';
                $messageType = 'success';
                break;
                
            case 'change_email':
                $newEmail = trim($_POST['new_email']);
                $password = $_POST['password'];
                
                changeAdminEmail($currentUserId, $newEmail, $password);
                
                // Update session data
                $_SESSION['user_email'] = $newEmail;
                
                $message = 'Email changed successfully!';
                $messageType = 'success';
                break;
                
            case 'upload_image':
                if (isset($_FILES['profile_image'])) {
                    $imageUrl = uploadProfileImage($_FILES['profile_image'], $currentUserId);

                    // Update profile with new image
                    updateAdminProfile($currentUserId, ['image' => $imageUrl]);

                    $message = 'Profile image updated successfully!';
                    $messageType = 'success';
                }
                break;

            case 'create_admin':
                $data = [
                    'name' => trim($_POST['admin_name']),
                    'email' => trim($_POST['admin_email']),
                    'phone' => trim($_POST['admin_phone']),
                    'date_of_birth' => $_POST['admin_date_of_birth'] ?: null,
                    'password' => $_POST['admin_password']
                ];

                $newAdmin = createAdminUser($data, $currentUserId);

                $message = 'New admin user created successfully!';
                $messageType = 'success';
                break;

            case 'toggle_admin_status':
                $targetAdminId = $_POST['admin_id'];
                $newStatus = toggleAdminStatus($targetAdminId, $currentUserId);

                $message = 'Admin status updated successfully!';
                $messageType = 'success';
                break;
        }
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

// Get current admin profile
$adminProfile = getAdminProfile($currentUserId);

if (!$adminProfile) {
    redirect('/admin');
}

// Get all admin users for management
if (!function_exists('getAllAdminUsers')) {
    // Fallback: manually include the functions file
    require_once __DIR__ . '/../../includes/admin_profile_functions.php';
}

$allAdmins = getAllAdminUsers();

$pageTitle = "Admin Profile";
include __DIR__ . '/../../includes/admin_header.php';
?>

<div class="min-h-screen bg-salon-black pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                    <!-- Header -->
                    <div class="bg-secondary-800 shadow rounded-lg p-6 mb-6">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <div>
                                <h1 class="text-2xl font-bold text-white">Admin Profile</h1>
                                <p class="mt-1 text-sm text-gray-300">Manage your admin account settings</p>
                            </div>
                        </div>
                    </div>

                    <!-- Message Display -->
                    <?php if ($message): ?>
                        <div class="mb-6 p-4 rounded-lg <?= $messageType === 'success' ? 'bg-green-100 border border-green-400 text-green-700' : 'bg-red-100 border border-red-400 text-red-700' ?>">
                            <?= htmlspecialchars($message) ?>
                        </div>
                    <?php endif; ?>

                    <!-- Profile Information -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Profile Details -->
                        <div class="bg-secondary-800 rounded-lg p-6">
                            <div class="flex items-center justify-between mb-6">
                                <h2 class="text-xl font-semibold text-white">Profile Information</h2>
                                <button onclick="openModal('profileModal')" class="bg-salon-gold text-black px-4 py-2 rounded-lg font-semibold hover:bg-gold-light transition-colors">
                                    <i class="fas fa-edit mr-2"></i>Edit Profile
                                </button>
                            </div>

                            <div class="space-y-4">
                                <!-- Profile Image -->
                                <div class="flex items-center space-x-4">
                                    <div class="relative">
                                        <?php if ($adminProfile['image']): ?>
                                            <img src="<?= htmlspecialchars($adminProfile['image']) ?>" alt="Profile" class="w-20 h-20 rounded-full object-cover">
                                        <?php else: ?>
                                            <div class="w-20 h-20 rounded-full bg-salon-gold flex items-center justify-center">
                                                <i class="fas fa-user text-2xl text-black"></i>
                                            </div>
                                        <?php endif; ?>
                                        <button onclick="openModal('imageModal')" class="absolute -bottom-1 -right-1 bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center hover:bg-blue-700 transition-colors">
                                            <i class="fas fa-camera text-xs"></i>
                                        </button>
                                    </div>
                                    <div>
                                        <h3 class="text-lg font-medium text-white"><?= htmlspecialchars($adminProfile['name']) ?></h3>
                                        <p class="text-sm text-gray-400"><?= htmlspecialchars($adminProfile['role']) ?></p>
                                    </div>
                                </div>

                                <!-- Profile Details -->
                                <div class="grid grid-cols-1 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300">Name</label>
                                        <p class="mt-1 text-white"><?= htmlspecialchars($adminProfile['name']) ?></p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300">Email</label>
                                        <p class="mt-1 text-white"><?= htmlspecialchars($adminProfile['email']) ?></p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300">Phone</label>
                                        <p class="mt-1 text-white"><?= $adminProfile['phone'] ? htmlspecialchars($adminProfile['phone']) : 'Not provided' ?></p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300">Date of Birth</label>
                                        <p class="mt-1 text-white"><?= $adminProfile['date_of_birth'] ? date('M j, Y', strtotime($adminProfile['date_of_birth'])) : 'Not provided' ?></p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300">Member Since</label>
                                        <p class="mt-1 text-white"><?= date('M j, Y', strtotime($adminProfile['created_at'])) ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Security Settings -->
                        <div class="bg-secondary-800 rounded-lg p-6">
                            <h2 class="text-xl font-semibold text-white mb-6">Security Settings</h2>
                            
                            <div class="space-y-4">
                                <!-- Change Password -->
                                <div class="border border-secondary-600 rounded-lg p-4">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h3 class="text-lg font-medium text-white">Password</h3>
                                            <p class="text-sm text-gray-400">Last updated: <?= date('M j, Y', strtotime($adminProfile['updated_at'])) ?></p>
                                        </div>
                                        <button onclick="openModal('passwordModal')" class="bg-blue-600 text-white px-4 py-2 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                                            Change Password
                                        </button>
                                    </div>
                                </div>

                                <!-- Change Email -->
                                <div class="border border-secondary-600 rounded-lg p-4">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h3 class="text-lg font-medium text-white">Email Address</h3>
                                            <p class="text-sm text-gray-400">Current: <?= htmlspecialchars($adminProfile['email']) ?></p>
                                        </div>
                                        <button onclick="openModal('emailModal')" class="bg-yellow-600 text-white px-4 py-2 rounded-lg font-semibold hover:bg-yellow-700 transition-colors">
                                            Change Email
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Admin Management Section -->
                    <div class="mt-6">
                        <div class="bg-secondary-800 rounded-lg p-6">
                            <div class="flex items-center justify-between mb-6">
                                <h2 class="text-xl font-semibold text-white">Admin Management</h2>
                                <button onclick="openModal('createAdminModal')" class="bg-salon-gold text-black px-4 py-2 rounded-lg font-semibold hover:bg-gold-light transition-colors">
                                    <i class="fas fa-plus mr-2"></i>Create New Admin
                                </button>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                <?php foreach ($allAdmins as $admin): ?>
                                    <div class="bg-secondary-700 rounded-lg p-4 border <?= $admin['is_active'] ? 'border-green-500' : 'border-red-500' ?>">
                                        <div class="flex items-center space-x-3 mb-3">
                                            <?php if ($admin['image']): ?>
                                                <img src="<?= htmlspecialchars($admin['image']) ?>" alt="Profile" class="w-12 h-12 rounded-full object-cover">
                                            <?php else: ?>
                                                <div class="w-12 h-12 rounded-full bg-salon-gold flex items-center justify-center">
                                                    <i class="fas fa-user text-black"></i>
                                                </div>
                                            <?php endif; ?>
                                            <div class="flex-1">
                                                <h3 class="text-white font-medium"><?= htmlspecialchars($admin['name']) ?></h3>
                                                <p class="text-sm text-gray-400"><?= htmlspecialchars($admin['email']) ?></p>
                                            </div>
                                            <div class="text-right">
                                                <span class="inline-block px-2 py-1 text-xs rounded-full <?= $admin['is_active'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' ?>">
                                                    <?= $admin['is_active'] ? 'Active' : 'Inactive' ?>
                                                </span>
                                            </div>
                                        </div>

                                        <div class="text-sm text-gray-400 mb-3">
                                            <p><i class="fas fa-phone mr-2"></i><?= $admin['phone'] ? htmlspecialchars($admin['phone']) : 'No phone' ?></p>
                                            <p><i class="fas fa-calendar mr-2"></i>Joined <?= date('M j, Y', strtotime($admin['created_at'])) ?></p>
                                        </div>

                                        <?php if ($admin['id'] !== $currentUserId): ?>
                                            <div class="flex gap-2">
                                                <button onclick="toggleAdminStatus('<?= $admin['id'] ?>', <?= $admin['is_active'] ? 'false' : 'true' ?>)"
                                                        class="flex-1 <?= $admin['is_active'] ? 'bg-red-600 hover:bg-red-700' : 'bg-green-600 hover:bg-green-700' ?> text-white py-2 px-3 rounded-lg text-sm font-medium transition-colors">
                                                    <?= $admin['is_active'] ? 'Deactivate' : 'Activate' ?>
                                                </button>
                                            </div>
                                        <?php else: ?>
                                            <div class="text-center text-sm text-gray-400 py-2">
                                                <i class="fas fa-user-shield mr-1"></i>Current User
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<!-- Profile Edit Modal -->
<div id="profileModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-secondary-800 rounded-lg max-w-md w-full p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-white">Edit Profile</h3>
                <button onclick="closeModal('profileModal')" class="text-gray-400 hover:text-white">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <form method="POST" class="space-y-4">
                <input type="hidden" name="action" value="update_profile">

                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-1">Name *</label>
                    <input type="text" name="name" value="<?= htmlspecialchars($adminProfile['name']) ?>" required
                           class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:border-salon-gold">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-1">Email *</label>
                    <input type="email" name="email" value="<?= htmlspecialchars($adminProfile['email']) ?>" required
                           class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:border-salon-gold">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-1">Phone</label>
                    <input type="tel" name="phone" value="<?= htmlspecialchars($adminProfile['phone'] ?? '') ?>"
                           class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:border-salon-gold">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-1">Date of Birth</label>
                    <input type="date" name="date_of_birth" value="<?= $adminProfile['date_of_birth'] ?>"
                           class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:border-salon-gold">
                </div>

                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" onclick="closeModal('profileModal')"
                            class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                        Cancel
                    </button>
                    <button type="submit"
                            class="px-4 py-2 bg-salon-gold text-black rounded-lg font-semibold hover:bg-gold-light transition-colors">
                        Update Profile
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Password Change Modal -->
<div id="passwordModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-secondary-800 rounded-lg max-w-md w-full p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-white">Change Password</h3>
                <button onclick="closeModal('passwordModal')" class="text-gray-400 hover:text-white">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <form method="POST" class="space-y-4">
                <input type="hidden" name="action" value="change_password">

                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-1">Current Password *</label>
                    <input type="password" name="current_password" required
                           class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:border-salon-gold">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-1">New Password *</label>
                    <input type="password" name="new_password" required minlength="8"
                           class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:border-salon-gold">
                    <p class="text-xs text-gray-400 mt-1">Must be at least 8 characters with uppercase, lowercase, and number</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-1">Confirm New Password *</label>
                    <input type="password" name="confirm_password" required minlength="8"
                           class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:border-salon-gold">
                </div>

                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" onclick="closeModal('passwordModal')"
                            class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                        Cancel
                    </button>
                    <button type="submit"
                            class="px-4 py-2 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                        Change Password
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Email Change Modal -->
<div id="emailModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-secondary-800 rounded-lg max-w-md w-full p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-white">Change Email</h3>
                <button onclick="closeModal('emailModal')" class="text-gray-400 hover:text-white">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <form method="POST" class="space-y-4">
                <input type="hidden" name="action" value="change_email">

                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-1">Current Email</label>
                    <input type="email" value="<?= htmlspecialchars($adminProfile['email']) ?>" disabled
                           class="w-full px-3 py-2 bg-secondary-600 border border-secondary-500 rounded-lg text-gray-400">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-1">New Email *</label>
                    <input type="email" name="new_email" required
                           class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:border-salon-gold">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-1">Password (for verification) *</label>
                    <input type="password" name="password" required
                           class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:border-salon-gold">
                </div>

                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" onclick="closeModal('emailModal')"
                            class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                        Cancel
                    </button>
                    <button type="submit"
                            class="px-4 py-2 bg-yellow-600 text-white rounded-lg font-semibold hover:bg-yellow-700 transition-colors">
                        Change Email
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Image Upload Modal -->
<div id="imageModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-secondary-800 rounded-lg max-w-md w-full p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-white">Update Profile Image</h3>
                <button onclick="closeModal('imageModal')" class="text-gray-400 hover:text-white">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <form method="POST" enctype="multipart/form-data" class="space-y-4">
                <input type="hidden" name="action" value="upload_image">

                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-1">Select Image</label>
                    <input type="file" name="profile_image" accept="image/*" required
                           class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:border-salon-gold">
                    <p class="text-xs text-gray-400 mt-1">Max size: 5MB. Formats: JPEG, PNG, GIF, WebP</p>
                </div>

                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" onclick="closeModal('imageModal')"
                            class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                        Cancel
                    </button>
                    <button type="submit"
                            class="px-4 py-2 bg-salon-gold text-black rounded-lg font-semibold hover:bg-gold-light transition-colors">
                        Upload Image
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Create Admin Modal -->
<div id="createAdminModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-secondary-800 rounded-lg p-6 w-full max-w-md mx-4">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-bold text-white">Create New Admin</h3>
            <button onclick="closeModal('createAdminModal')" class="text-gray-400 hover:text-white">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <form method="POST" class="space-y-4">
            <input type="hidden" name="action" value="create_admin">

            <div>
                <label class="block text-sm font-medium text-gray-300 mb-1">Full Name *</label>
                <input type="text" name="admin_name" required
                       class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:border-salon-gold">
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-300 mb-1">Email Address *</label>
                <input type="email" name="admin_email" required
                       class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:border-salon-gold">
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-300 mb-1">Phone Number</label>
                <input type="tel" name="admin_phone"
                       class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:border-salon-gold">
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-300 mb-1">Date of Birth</label>
                <input type="date" name="admin_date_of_birth"
                       class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:border-salon-gold">
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-300 mb-1">Password *</label>
                <input type="password" name="admin_password" required
                       class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:border-salon-gold">
                <p class="text-xs text-gray-400 mt-1">Minimum 8 characters with uppercase, lowercase, and number</p>
            </div>

            <div class="flex justify-end space-x-3 pt-4">
                <button type="button" onclick="closeModal('createAdminModal')"
                        class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                    Cancel
                </button>
                <button type="submit"
                        class="px-4 py-2 bg-salon-gold text-black rounded-lg font-semibold hover:bg-gold-light transition-colors">
                    Create Admin
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function openModal(modalId) {
    document.getElementById(modalId).classList.remove('hidden');
}

function closeModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
}

// Close modal when clicking outside
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('fixed')) {
        e.target.classList.add('hidden');
    }
});

// Admin management functions
function toggleAdminStatus(adminId, activate) {
    if (confirm(`Are you sure you want to ${activate ? 'activate' : 'deactivate'} this admin user?`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="toggle_admin_status">
            <input type="hidden" name="admin_id" value="${adminId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// Password confirmation validation
document.querySelector('input[name="confirm_password"]').addEventListener('input', function() {
    const newPassword = document.querySelector('input[name="new_password"]').value;
    const confirmPassword = this.value;

    if (newPassword !== confirmPassword) {
        this.setCustomValidity('Passwords do not match');
    } else {
        this.setCustomValidity('');
    }
});
</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>
