<?php
require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/admin_profile_functions.php';
require_once __DIR__ . '/../../includes/admin_2fa_functions.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    redirect('/auth/login.php');
}

$currentUserId = $_SESSION['user_id'];
$message = '';
$messageType = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'update_profile':
                $data = [
                    'name' => trim($_POST['name']),
                    'email' => trim($_POST['email']),
                    'phone' => trim($_POST['phone']),
                    'date_of_birth' => $_POST['date_of_birth'] ?: null
                ];
                
                updateAdminProfile($currentUserId, $data);
                
                // Update session data
                $_SESSION['user_name'] = $data['name'];
                
                $message = 'Profile updated successfully!';
                $messageType = 'success';
                break;
                
            case 'change_password':
                $currentPassword = $_POST['current_password'];
                $newPassword = $_POST['new_password'];
                $confirmPassword = $_POST['confirm_password'];
                
                changeAdminPassword($currentUserId, $currentPassword, $newPassword, $confirmPassword);
                
                $message = 'Password changed successfully!';
                $messageType = 'success';
                break;
                
            case 'change_email':
                $newEmail = trim($_POST['new_email']);
                $password = $_POST['password'];
                
                changeAdminEmail($currentUserId, $newEmail, $password);
                
                // Update session data
                $_SESSION['user_email'] = $newEmail;
                
                $message = 'Email changed successfully!';
                $messageType = 'success';
                break;
                
            case 'upload_image':
                if (isset($_FILES['profile_image'])) {
                    $imageUrl = uploadProfileImage($_FILES['profile_image'], $currentUserId);

                    // Update profile with new image
                    updateAdminProfile($currentUserId, ['image' => $imageUrl]);

                    $message = 'Profile image updated successfully!';
                    $messageType = 'success';
                }
                break;

            case 'create_admin':
                $data = [
                    'name' => trim($_POST['admin_name']),
                    'email' => trim($_POST['admin_email']),
                    'phone' => trim($_POST['admin_phone']),
                    'date_of_birth' => $_POST['admin_date_of_birth'] ?: null,
                    'password' => $_POST['admin_password']
                ];

                $newAdmin = createAdminUser($data, $currentUserId);

                $message = 'New admin user created successfully!';
                $messageType = 'success';
                break;

            case 'toggle_admin_status':
                $targetAdminId = $_POST['admin_id'];
                $newStatus = toggleAdminStatus($targetAdminId, $currentUserId);

                $message = 'Admin status updated successfully!';
                $messageType = 'success';
                break;

            case 'edit_admin':
                $targetAdminId = $_POST['admin_id'];
                $data = [
                    'name' => trim($_POST['edit_admin_name']),
                    'email' => trim($_POST['edit_admin_email']),
                    'phone' => trim($_POST['edit_admin_phone']),
                    'date_of_birth' => $_POST['edit_admin_date_of_birth'] ?: null
                ];

                updateAdminUser($targetAdminId, $data, $currentUserId);

                $message = 'Admin user updated successfully!';
                $messageType = 'success';
                break;

            case 'reset_admin_password':
                $targetAdminId = $_POST['admin_id'];
                $newPassword = $_POST['new_admin_password'];

                resetAdminPassword($targetAdminId, $newPassword, $currentUserId);

                $message = 'Admin password reset successfully!';
                $messageType = 'success';
                break;

            case 'delete_admin':
                $targetAdminId = $_POST['admin_id'];

                deleteAdminUser($targetAdminId, $currentUserId);

                $message = 'Admin user deleted successfully!';
                $messageType = 'success';
                break;

            case 'enable_2fa':
                $emailEnabled = isset($_POST['email_2fa']) ? 1 : 0;
                $backupEnabled = isset($_POST['backup_codes']) ? 1 : 0;

                if (!$emailEnabled && !$backupEnabled) {
                    throw new Exception('At least one 2FA method must be enabled');
                }

                $result = enableAdmin2FA($currentUserId, $emailEnabled, $backupEnabled);

                if (!$result['success']) {
                    throw new Exception($result['error']);
                }

                $message = 'Two-factor authentication enabled successfully!';
                $messageType = 'success';
                break;

            case 'disable_2fa':
                $result = disableAdmin2FA($currentUserId);

                if (!$result['success']) {
                    throw new Exception($result['error']);
                }

                $message = 'Two-factor authentication disabled successfully!';
                $messageType = 'success';
                break;

            case 'generate_backup_codes':
                $newCodes = generateBackupCodes(10);
                $result = storeAdminBackupCodes($currentUserId, $newCodes);

                if (!$result['success']) {
                    throw new Exception($result['error']);
                }

                $_SESSION['new_backup_codes'] = $newCodes;
                $message = 'New backup codes generated successfully! Please save them securely.';
                $messageType = 'success';
                break;
        }
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

// Get current admin profile
$adminProfile = getAdminProfile($currentUserId);

if (!$adminProfile) {
    redirect('/admin');
}

// Get 2FA settings
$twoFASettings = getAdmin2FASettings($currentUserId);
$remainingBackupCodes = getRemainingBackupCodesCount($currentUserId);

// Check for new backup codes to display
$newBackupCodes = $_SESSION['new_backup_codes'] ?? null;
if ($newBackupCodes) {
    unset($_SESSION['new_backup_codes']);
}

// Get all admin users for management
if (!function_exists('getAllAdminUsers')) {
    // Fallback: manually include the functions file
    require_once __DIR__ . '/../../includes/admin_profile_functions.php';
}

$allAdmins = getAllAdminUsers();

$pageTitle = "Admin Profile";
include __DIR__ . '/../../includes/admin_header.php';
?>

<div class="min-h-screen bg-salon-black pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                    <!-- Header -->
                    <div class="bg-secondary-800 shadow rounded-lg p-6 mb-6">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <div>
                                <h1 class="text-2xl font-bold text-white">Admin Profile</h1>
                                <p class="mt-1 text-sm text-gray-300">Manage your admin account settings</p>
                            </div>
                        </div>
                    </div>

                    <!-- Message Display -->
                    <?php if ($message): ?>
                        <div class="mb-6 p-4 rounded-lg <?= $messageType === 'success' ? 'bg-green-100 border border-green-400 text-green-700' : 'bg-red-100 border border-red-400 text-red-700' ?>">
                            <?= htmlspecialchars($message) ?>
                        </div>
                    <?php endif; ?>

                    <!-- Profile Information -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Profile Details -->
                        <div class="bg-secondary-800 rounded-lg p-6">
                            <div class="flex items-center justify-between mb-6">
                                <h2 class="text-xl font-semibold text-white">Profile Information</h2>
                                <button onclick="openModal('profileModal')" class="bg-salon-gold text-black px-4 py-2 rounded-lg font-semibold hover:bg-gold-light transition-colors">
                                    <i class="fas fa-edit mr-2"></i>Edit Profile
                                </button>
                            </div>

                            <div class="space-y-4">
                                <!-- Profile Image -->
                                <div class="flex items-center space-x-4">
                                    <div class="relative">
                                        <?php if ($adminProfile['image']): ?>
                                            <img src="<?= htmlspecialchars($adminProfile['image']) ?>" alt="Profile" class="w-20 h-20 rounded-full object-cover">
                                        <?php else: ?>
                                            <div class="w-20 h-20 rounded-full bg-salon-gold flex items-center justify-center">
                                                <i class="fas fa-user text-2xl text-black"></i>
                                            </div>
                                        <?php endif; ?>
                                        <button onclick="openModal('imageModal')" class="absolute -bottom-1 -right-1 bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center hover:bg-blue-700 transition-colors">
                                            <i class="fas fa-camera text-xs"></i>
                                        </button>
                                    </div>
                                    <div>
                                        <h3 class="text-lg font-medium text-white"><?= htmlspecialchars($adminProfile['name']) ?></h3>
                                        <p class="text-sm text-gray-400"><?= htmlspecialchars($adminProfile['role']) ?></p>
                                    </div>
                                </div>

                                <!-- Profile Details -->
                                <div class="grid grid-cols-1 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300">Name</label>
                                        <p class="mt-1 text-white"><?= htmlspecialchars($adminProfile['name']) ?></p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300">Email</label>
                                        <p class="mt-1 text-white"><?= htmlspecialchars($adminProfile['email']) ?></p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300">Phone</label>
                                        <p class="mt-1 text-white"><?= $adminProfile['phone'] ? htmlspecialchars($adminProfile['phone']) : 'Not provided' ?></p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300">Date of Birth</label>
                                        <p class="mt-1 text-white"><?= $adminProfile['date_of_birth'] ? date('M j, Y', strtotime($adminProfile['date_of_birth'])) : 'Not provided' ?></p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300">Member Since</label>
                                        <p class="mt-1 text-white"><?= date('M j, Y', strtotime($adminProfile['created_at'])) ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Security Settings -->
                        <div class="bg-secondary-800 rounded-lg p-6">
                            <h2 class="text-xl font-semibold text-white mb-6">Security Settings</h2>
                            
                            <div class="space-y-4">
                                <!-- Change Password -->
                                <div class="border border-secondary-600 rounded-lg p-4">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h3 class="text-lg font-medium text-white">Password</h3>
                                            <p class="text-sm text-gray-400">Last updated: <?= date('M j, Y', strtotime($adminProfile['updated_at'])) ?></p>
                                        </div>
                                        <button onclick="openModal('passwordModal')" class="bg-blue-600 text-white px-4 py-2 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                                            Change Password
                                        </button>
                                    </div>
                                </div>

                                <!-- Change Email -->
                                <div class="border border-secondary-600 rounded-lg p-4">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h3 class="text-lg font-medium text-white">Email Address</h3>
                                            <p class="text-sm text-gray-400">Current: <?= htmlspecialchars($adminProfile['email']) ?></p>
                                        </div>
                                        <button onclick="openModal('emailModal')" class="bg-yellow-600 text-white px-4 py-2 rounded-lg font-semibold hover:bg-yellow-700 transition-colors">
                                            Change Email
                                        </button>
                                    </div>
                                </div>

                                <!-- Two-Factor Authentication -->
                                <div class="border border-secondary-600 rounded-lg p-4">
                                    <div class="flex items-center justify-between mb-4">
                                        <div>
                                            <h3 class="text-lg font-medium text-white">Two-Factor Authentication</h3>
                                            <p class="text-sm text-gray-400">
                                                Status:
                                                <?php if ($twoFASettings['is_enabled']): ?>
                                                    <span class="text-green-400 font-medium">Enabled</span>
                                                <?php else: ?>
                                                    <span class="text-red-400 font-medium">Disabled</span>
                                                <?php endif; ?>
                                            </p>
                                        </div>
                                        <?php if ($twoFASettings['is_enabled']): ?>
                                            <button onclick="openModal('disable2FAModal')" class="bg-red-600 text-white px-4 py-2 rounded-lg font-semibold hover:bg-red-700 transition-colors">
                                                Disable 2FA
                                            </button>
                                        <?php else: ?>
                                            <button onclick="openModal('enable2FAModal')" class="bg-green-600 text-white px-4 py-2 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                                                Enable 2FA
                                            </button>
                                        <?php endif; ?>
                                    </div>

                                    <?php if ($twoFASettings['is_enabled']): ?>
                                        <!-- 2FA Status Details -->
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                                            <div class="bg-secondary-700 rounded-lg p-3">
                                                <div class="flex items-center">
                                                    <svg class="w-5 h-5 text-blue-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                                    </svg>
                                                    <span class="text-white font-medium">Email Verification</span>
                                                </div>
                                                <p class="text-sm text-gray-400 mt-1">
                                                    <?= $twoFASettings['email_2fa_enabled'] ? 'Enabled' : 'Disabled' ?>
                                                </p>
                                            </div>

                                            <div class="bg-secondary-700 rounded-lg p-3">
                                                <div class="flex items-center">
                                                    <svg class="w-5 h-5 text-yellow-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"></path>
                                                    </svg>
                                                    <span class="text-white font-medium">Backup Codes</span>
                                                </div>
                                                <p class="text-sm text-gray-400 mt-1">
                                                    <?= $remainingBackupCodes ?> remaining
                                                </p>
                                            </div>
                                        </div>

                                        <!-- 2FA Actions -->
                                        <div class="flex flex-wrap gap-2 mt-4">
                                            <?php if ($twoFASettings['backup_codes_enabled']): ?>
                                                <a href="/admin/auth/backup-codes.php" class="bg-blue-600 text-white px-3 py-2 rounded text-sm font-medium hover:bg-blue-700 transition-colors">
                                                    Manage Backup Codes
                                                </a>
                                                <?php if ($remainingBackupCodes <= 2): ?>
                                                    <button onclick="openModal('generateBackupCodesModal')" class="bg-yellow-600 text-white px-3 py-2 rounded text-sm font-medium hover:bg-yellow-700 transition-colors">
                                                        Generate New Codes
                                                    </button>
                                                <?php endif; ?>
                                            <?php endif; ?>
                                        </div>

                                        <?php if ($remainingBackupCodes <= 2): ?>
                                            <div class="mt-4 p-3 bg-yellow-500/20 border border-yellow-500/50 rounded-lg">
                                                <div class="flex items-center">
                                                    <svg class="w-5 h-5 text-yellow-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                                    </svg>
                                                    <span class="text-yellow-200 font-medium">Low backup codes warning</span>
                                                </div>
                                                <p class="text-yellow-300 text-sm mt-1">
                                                    You have only <?= $remainingBackupCodes ?> backup codes remaining. Generate new ones to ensure account recovery.
                                                </p>
                                            </div>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <!-- 2FA Benefits -->
                                        <div class="bg-blue-500/20 border border-blue-500/50 rounded-lg p-4 mt-4">
                                            <h4 class="text-blue-200 font-medium mb-2">Enhanced Security Benefits</h4>
                                            <ul class="text-blue-300 text-sm space-y-1">
                                                <li>• Email verification codes for secure login</li>
                                                <li>• Backup codes for emergency access</li>
                                                <li>• Protection against unauthorized access</li>
                                                <li>• Audit logging for security monitoring</li>
                                            </ul>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Admin Management Section -->
                    <div class="mt-6">
                        <div class="bg-secondary-800 rounded-lg p-6">
                            <div class="flex items-center justify-between mb-6">
                                <h2 class="text-xl font-semibold text-white">Admin Management</h2>
                                <button onclick="openModal('createAdminModal')" class="bg-salon-gold text-black px-4 py-2 rounded-lg font-semibold hover:bg-gold-light transition-colors">
                                    <i class="fas fa-plus mr-2"></i>Create New Admin
                                </button>
                            </div>

                            <!-- Admin Statistics -->
                            <?php
                            $adminStats = getAdminStatistics();
                            ?>
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                                <div class="bg-secondary-700 rounded-lg p-4 text-center">
                                    <div class="text-2xl font-bold text-salon-gold"><?= $adminStats['total_admins'] ?></div>
                                    <div class="text-sm text-gray-400">Total Admins</div>
                                </div>
                                <div class="bg-secondary-700 rounded-lg p-4 text-center">
                                    <div class="text-2xl font-bold text-green-400"><?= $adminStats['active_admins'] ?></div>
                                    <div class="text-sm text-gray-400">Active</div>
                                </div>
                                <div class="bg-secondary-700 rounded-lg p-4 text-center">
                                    <div class="text-2xl font-bold text-red-400"><?= $adminStats['inactive_admins'] ?></div>
                                    <div class="text-sm text-gray-400">Inactive</div>
                                </div>
                                <div class="bg-secondary-700 rounded-lg p-4 text-center">
                                    <div class="text-2xl font-bold text-blue-400"><?= $adminStats['recent_activities'] ?></div>
                                    <div class="text-sm text-gray-400">Recent Activities</div>
                                </div>
                            </div>

                            <!-- Search and Filter -->
                            <div class="flex flex-col md:flex-row gap-4 mb-6">
                                <div class="flex-1">
                                    <input type="text" id="adminSearch" placeholder="Search admins by name or email..."
                                           class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:border-salon-gold">
                                </div>
                                <div class="flex gap-2">
                                    <select id="statusFilter" class="px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:border-salon-gold">
                                        <option value="all">All Status</option>
                                        <option value="active">Active Only</option>
                                        <option value="inactive">Inactive Only</option>
                                    </select>
                                    <button onclick="filterAdmins()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                                        <i class="fas fa-search mr-1"></i>Filter
                                    </button>
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                <?php foreach ($allAdmins as $admin): ?>
                                    <div class="admin-card bg-secondary-700 rounded-lg p-4 border <?= $admin['is_active'] ? 'border-green-500' : 'border-red-500' ?>"
                                         data-name="<?= htmlspecialchars($admin['name']) ?>"
                                         data-email="<?= htmlspecialchars($admin['email']) ?>"
                                         data-status="<?= $admin['is_active'] ? 'active' : 'inactive' ?>">
                                        <div class="flex items-center space-x-3 mb-3">
                                            <?php if ($admin['image']): ?>
                                                <img src="<?= htmlspecialchars($admin['image']) ?>" alt="Profile" class="w-12 h-12 rounded-full object-cover">
                                            <?php else: ?>
                                                <div class="w-12 h-12 rounded-full bg-salon-gold flex items-center justify-center">
                                                    <i class="fas fa-user text-black"></i>
                                                </div>
                                            <?php endif; ?>
                                            <div class="flex-1">
                                                <h3 class="text-white font-medium"><?= htmlspecialchars($admin['name']) ?></h3>
                                                <p class="text-sm text-gray-400"><?= htmlspecialchars($admin['email']) ?></p>
                                            </div>
                                            <div class="text-right">
                                                <span class="inline-block px-2 py-1 text-xs rounded-full <?= $admin['is_active'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' ?>">
                                                    <?= $admin['is_active'] ? 'Active' : 'Inactive' ?>
                                                </span>
                                            </div>
                                        </div>

                                        <div class="text-sm text-gray-400 mb-3">
                                            <p><i class="fas fa-phone mr-2"></i><?= $admin['phone'] ? htmlspecialchars($admin['phone']) : 'No phone' ?></p>
                                            <p><i class="fas fa-calendar mr-2"></i>Joined <?= date('M j, Y', strtotime($admin['created_at'])) ?></p>
                                        </div>

                                        <?php if ($admin['id'] !== $currentUserId): ?>
                                            <div class="space-y-2">
                                                <div class="flex gap-2">
                                                    <button onclick="editAdmin('<?= $admin['id'] ?>', '<?= htmlspecialchars($admin['name']) ?>', '<?= htmlspecialchars($admin['email']) ?>', '<?= htmlspecialchars($admin['phone']) ?>', '<?= $admin['date_of_birth'] ?>')"
                                                            class="flex-1 bg-blue-600 text-white py-2 px-3 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors">
                                                        <i class="fas fa-edit mr-1"></i>Edit
                                                    </button>
                                                    <button onclick="resetAdminPassword('<?= $admin['id'] ?>', '<?= htmlspecialchars($admin['name']) ?>')"
                                                            class="flex-1 bg-yellow-600 text-white py-2 px-3 rounded-lg text-sm font-medium hover:bg-yellow-700 transition-colors">
                                                        <i class="fas fa-key mr-1"></i>Reset
                                                    </button>
                                                </div>
                                                <div class="flex gap-2">
                                                    <button onclick="toggleAdminStatus('<?= $admin['id'] ?>', <?= $admin['is_active'] ? 'false' : 'true' ?>)"
                                                            class="flex-1 <?= $admin['is_active'] ? 'bg-red-600 hover:bg-red-700' : 'bg-green-600 hover:bg-green-700' ?> text-white py-2 px-3 rounded-lg text-sm font-medium transition-colors">
                                                        <?= $admin['is_active'] ? 'Deactivate' : 'Activate' ?>
                                                    </button>
                                                    <button onclick="deleteAdmin('<?= $admin['id'] ?>', '<?= htmlspecialchars($admin['name']) ?>')"
                                                            class="flex-1 bg-red-800 text-white py-2 px-3 rounded-lg text-sm font-medium hover:bg-red-900 transition-colors">
                                                        <i class="fas fa-trash mr-1"></i>Delete
                                                    </button>
                                                </div>
                                            </div>
                                        <?php else: ?>
                                            <div class="text-center text-sm text-gray-400 py-2">
                                                <i class="fas fa-user-shield mr-1"></i>Current User
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Admin Activity Log Section -->
                    <div class="mt-6">
                        <div class="bg-secondary-800 rounded-lg p-6">
                            <div class="flex items-center justify-between mb-6">
                                <h2 class="text-xl font-semibold text-white">Recent Admin Activities</h2>
                                <button onclick="toggleActivityLog()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                                    <i class="fas fa-history mr-2"></i>View All Logs
                                </button>
                            </div>

                            <div id="activityLogContainer" class="hidden">
                                <?php
                                $recentLogs = getAdminLogs(null, 10, 0);
                                ?>
                                <div class="space-y-3">
                                    <?php foreach ($recentLogs as $log): ?>
                                        <div class="bg-secondary-700 rounded-lg p-4 border-l-4 border-blue-500">
                                            <div class="flex items-start justify-between">
                                                <div class="flex-1">
                                                    <div class="flex items-center space-x-2 mb-1">
                                                        <span class="text-white font-medium"><?= htmlspecialchars($log['admin_name']) ?></span>
                                                        <span class="text-xs bg-blue-600 text-white px-2 py-1 rounded"><?= htmlspecialchars($log['action']) ?></span>
                                                    </div>
                                                    <p class="text-gray-300 text-sm"><?= htmlspecialchars($log['description']) ?></p>
                                                    <?php if ($log['metadata']): ?>
                                                        <div class="mt-2 text-xs text-gray-400">
                                                            <details>
                                                                <summary class="cursor-pointer hover:text-gray-300">View Details</summary>
                                                                <pre class="mt-1 bg-secondary-600 p-2 rounded text-xs overflow-x-auto"><?= htmlspecialchars(json_encode(json_decode($log['metadata']), JSON_PRETTY_PRINT)) ?></pre>
                                                            </details>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="text-right text-xs text-gray-400">
                                                    <div><?= date('M j, Y', strtotime($log['created_at'])) ?></div>
                                                    <div><?= date('H:i:s', strtotime($log['created_at'])) ?></div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>

                                    <?php if (empty($recentLogs)): ?>
                                        <div class="text-center text-gray-400 py-8">
                                            <i class="fas fa-history text-4xl mb-4"></i>
                                            <p>No admin activities recorded yet.</p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<!-- Profile Edit Modal -->
<div id="profileModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-secondary-800 rounded-lg max-w-md w-full p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-white">Edit Profile</h3>
                <button onclick="closeModal('profileModal')" class="text-gray-400 hover:text-white">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <form method="POST" class="space-y-4">
                <input type="hidden" name="action" value="update_profile">

                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-1">Name *</label>
                    <input type="text" name="name" value="<?= htmlspecialchars($adminProfile['name']) ?>" required
                           class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:border-salon-gold">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-1">Email *</label>
                    <input type="email" name="email" value="<?= htmlspecialchars($adminProfile['email']) ?>" required
                           class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:border-salon-gold">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-1">Phone</label>
                    <input type="tel" name="phone" value="<?= htmlspecialchars($adminProfile['phone'] ?? '') ?>"
                           class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:border-salon-gold">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-1">Date of Birth</label>
                    <input type="date" name="date_of_birth" value="<?= $adminProfile['date_of_birth'] ?>"
                           class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:border-salon-gold">
                </div>

                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" onclick="closeModal('profileModal')"
                            class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                        Cancel
                    </button>
                    <button type="submit"
                            class="px-4 py-2 bg-salon-gold text-black rounded-lg font-semibold hover:bg-gold-light transition-colors">
                        Update Profile
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Password Change Modal -->
<div id="passwordModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-secondary-800 rounded-lg max-w-md w-full p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-white">Change Password</h3>
                <button onclick="closeModal('passwordModal')" class="text-gray-400 hover:text-white">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <form method="POST" class="space-y-4">
                <input type="hidden" name="action" value="change_password">

                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-1">Current Password *</label>
                    <input type="password" name="current_password" required
                           class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:border-salon-gold">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-1">New Password *</label>
                    <input type="password" name="new_password" required minlength="8"
                           class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:border-salon-gold">
                    <p class="text-xs text-gray-400 mt-1">Must be at least 8 characters with uppercase, lowercase, and number</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-1">Confirm New Password *</label>
                    <input type="password" name="confirm_password" required minlength="8"
                           class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:border-salon-gold">
                </div>

                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" onclick="closeModal('passwordModal')"
                            class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                        Cancel
                    </button>
                    <button type="submit"
                            class="px-4 py-2 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                        Change Password
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Email Change Modal -->
<div id="emailModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-secondary-800 rounded-lg max-w-md w-full p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-white">Change Email</h3>
                <button onclick="closeModal('emailModal')" class="text-gray-400 hover:text-white">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <form method="POST" class="space-y-4">
                <input type="hidden" name="action" value="change_email">

                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-1">Current Email</label>
                    <input type="email" value="<?= htmlspecialchars($adminProfile['email']) ?>" disabled
                           class="w-full px-3 py-2 bg-secondary-600 border border-secondary-500 rounded-lg text-gray-400">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-1">New Email *</label>
                    <input type="email" name="new_email" required
                           class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:border-salon-gold">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-1">Password (for verification) *</label>
                    <input type="password" name="password" required
                           class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:border-salon-gold">
                </div>

                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" onclick="closeModal('emailModal')"
                            class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                        Cancel
                    </button>
                    <button type="submit"
                            class="px-4 py-2 bg-yellow-600 text-white rounded-lg font-semibold hover:bg-yellow-700 transition-colors">
                        Change Email
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Image Upload Modal -->
<div id="imageModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-secondary-800 rounded-lg max-w-md w-full p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-white">Update Profile Image</h3>
                <button onclick="closeModal('imageModal')" class="text-gray-400 hover:text-white">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <form method="POST" enctype="multipart/form-data" class="space-y-4">
                <input type="hidden" name="action" value="upload_image">

                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-1">Select Image</label>
                    <input type="file" name="profile_image" accept="image/*" required
                           class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:border-salon-gold">
                    <p class="text-xs text-gray-400 mt-1">Max size: 5MB. Formats: JPEG, PNG, GIF, WebP</p>
                </div>

                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" onclick="closeModal('imageModal')"
                            class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                        Cancel
                    </button>
                    <button type="submit"
                            class="px-4 py-2 bg-salon-gold text-black rounded-lg font-semibold hover:bg-gold-light transition-colors">
                        Upload Image
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Create Admin Modal -->
<div id="createAdminModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-secondary-800 rounded-lg p-6 w-full max-w-md mx-4">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-bold text-white">Create New Admin</h3>
            <button onclick="closeModal('createAdminModal')" class="text-gray-400 hover:text-white">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <form method="POST" class="space-y-4">
            <input type="hidden" name="action" value="create_admin">

            <div>
                <label class="block text-sm font-medium text-gray-300 mb-1">Full Name *</label>
                <input type="text" name="admin_name" required
                       class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:border-salon-gold">
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-300 mb-1">Email Address *</label>
                <input type="email" name="admin_email" required
                       class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:border-salon-gold">
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-300 mb-1">Phone Number</label>
                <input type="tel" name="admin_phone"
                       class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:border-salon-gold">
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-300 mb-1">Date of Birth</label>
                <input type="date" name="admin_date_of_birth"
                       class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:border-salon-gold">
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-300 mb-1">Password *</label>
                <input type="password" name="admin_password" required
                       class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:border-salon-gold">
                <p class="text-xs text-gray-400 mt-1">Minimum 8 characters with uppercase, lowercase, and number</p>
            </div>

            <div class="flex justify-end space-x-3 pt-4">
                <button type="button" onclick="closeModal('createAdminModal')"
                        class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                    Cancel
                </button>
                <button type="submit"
                        class="px-4 py-2 bg-salon-gold text-black rounded-lg font-semibold hover:bg-gold-light transition-colors">
                    Create Admin
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Edit Admin Modal -->
<div id="editAdminModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-secondary-800 rounded-lg p-6 w-full max-w-md mx-4">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-bold text-white">Edit Admin User</h3>
            <button onclick="closeModal('editAdminModal')" class="text-gray-400 hover:text-white">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <form method="POST" class="space-y-4">
            <input type="hidden" name="action" value="edit_admin">
            <input type="hidden" name="admin_id" id="editAdminId">

            <div>
                <label class="block text-sm font-medium text-gray-300 mb-1">Full Name *</label>
                <input type="text" name="edit_admin_name" id="editAdminName" required
                       class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:border-salon-gold">
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-300 mb-1">Email Address *</label>
                <input type="email" name="edit_admin_email" id="editAdminEmail" required
                       class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:border-salon-gold">
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-300 mb-1">Phone Number</label>
                <input type="tel" name="edit_admin_phone" id="editAdminPhone"
                       class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:border-salon-gold">
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-300 mb-1">Date of Birth</label>
                <input type="date" name="edit_admin_date_of_birth" id="editAdminDateOfBirth"
                       class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:border-salon-gold">
            </div>

            <div class="flex justify-end space-x-3 pt-4">
                <button type="button" onclick="closeModal('editAdminModal')"
                        class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                    Cancel
                </button>
                <button type="submit"
                        class="px-4 py-2 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                    Update Admin
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Reset Password Modal -->
<div id="resetPasswordModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-secondary-800 rounded-lg p-6 w-full max-w-md mx-4">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-bold text-white">Reset Admin Password</h3>
            <button onclick="closeModal('resetPasswordModal')" class="text-gray-400 hover:text-white">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <form method="POST" class="space-y-4">
            <input type="hidden" name="action" value="reset_admin_password">
            <input type="hidden" name="admin_id" id="resetPasswordAdminId">

            <div>
                <label class="block text-sm font-medium text-gray-300 mb-1">Admin User</label>
                <input type="text" id="resetPasswordAdminName" disabled
                       class="w-full px-3 py-2 bg-secondary-600 border border-secondary-500 rounded-lg text-gray-400">
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-300 mb-1">New Password *</label>
                <input type="password" name="new_admin_password" required
                       class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:border-salon-gold">
                <p class="text-xs text-gray-400 mt-1">Minimum 8 characters with uppercase, lowercase, and number</p>
            </div>

            <div class="flex justify-end space-x-3 pt-4">
                <button type="button" onclick="closeModal('resetPasswordModal')"
                        class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                    Cancel
                </button>
                <button type="submit"
                        class="px-4 py-2 bg-yellow-600 text-white rounded-lg font-semibold hover:bg-yellow-700 transition-colors">
                    Reset Password
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Enable 2FA Modal -->
<div id="enable2FAModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-secondary-800 rounded-lg p-6 w-full max-w-md mx-4">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-bold text-white">Enable Two-Factor Authentication</h3>
            <button onclick="closeModal('enable2FAModal')" class="text-gray-400 hover:text-white">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <form method="POST">
            <input type="hidden" name="action" value="enable_2fa">

            <div class="mb-4">
                <p class="text-gray-300 text-sm mb-4">
                    Select the 2FA methods you want to enable for enhanced security:
                </p>

                <div class="space-y-3">
                    <label class="flex items-center">
                        <input type="checkbox" name="email_2fa" value="1" checked class="mr-3 rounded">
                        <div>
                            <span class="text-white font-medium">Email Verification</span>
                            <p class="text-gray-400 text-sm">Receive 6-digit codes via email</p>
                        </div>
                    </label>

                    <label class="flex items-center">
                        <input type="checkbox" name="backup_codes" value="1" checked class="mr-3 rounded">
                        <div>
                            <span class="text-white font-medium">Backup Codes</span>
                            <p class="text-gray-400 text-sm">Emergency access codes for account recovery</p>
                        </div>
                    </label>
                </div>
            </div>

            <div class="bg-blue-500/20 border border-blue-500/50 rounded-lg p-3 mb-4">
                <p class="text-blue-200 text-sm">
                    <strong>Note:</strong> After enabling 2FA, you'll need to verify your identity with the selected methods each time you log in.
                </p>
            </div>

            <div class="flex gap-3">
                <button type="button" onclick="closeModal('enable2FAModal')"
                        class="flex-1 bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors">
                    Cancel
                </button>
                <button type="submit"
                        class="flex-1 bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors">
                    Enable 2FA
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Disable 2FA Modal -->
<div id="disable2FAModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-secondary-800 rounded-lg p-6 w-full max-w-md mx-4">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-bold text-white">Disable Two-Factor Authentication</h3>
            <button onclick="closeModal('disable2FAModal')" class="text-gray-400 hover:text-white">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <form method="POST">
            <input type="hidden" name="action" value="disable_2fa">

            <div class="mb-4">
                <div class="bg-red-500/20 border border-red-500/50 rounded-lg p-4 mb-4">
                    <div class="flex items-start">
                        <svg class="w-6 h-6 text-red-400 mr-3 mt-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                        <div>
                            <h4 class="text-red-200 font-semibold mb-2">Security Warning</h4>
                            <p class="text-red-300 text-sm">
                                Disabling 2FA will reduce your account security. Your account will only be protected by your password.
                            </p>
                        </div>
                    </div>
                </div>

                <p class="text-gray-300 text-sm">
                    Are you sure you want to disable two-factor authentication? This action will:
                </p>
                <ul class="text-gray-400 text-sm mt-2 space-y-1 ml-4">
                    <li>• Remove email verification requirement</li>
                    <li>• Invalidate all backup codes</li>
                    <li>• Reduce account security</li>
                </ul>
            </div>

            <div class="flex gap-3">
                <button type="button" onclick="closeModal('disable2FAModal')"
                        class="flex-1 bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors">
                    Cancel
                </button>
                <button type="submit"
                        class="flex-1 bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700 transition-colors">
                    Disable 2FA
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Generate Backup Codes Modal -->
<div id="generateBackupCodesModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-secondary-800 rounded-lg p-6 w-full max-w-md mx-4">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-bold text-white">Generate New Backup Codes</h3>
            <button onclick="closeModal('generateBackupCodesModal')" class="text-gray-400 hover:text-white">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <form method="POST">
            <input type="hidden" name="action" value="generate_backup_codes">

            <div class="mb-4">
                <div class="bg-yellow-500/20 border border-yellow-500/50 rounded-lg p-4 mb-4">
                    <div class="flex items-start">
                        <svg class="w-6 h-6 text-yellow-400 mr-3 mt-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                        <div>
                            <h4 class="text-yellow-200 font-semibold mb-2">Important Notice</h4>
                            <p class="text-yellow-300 text-sm">
                                Generating new backup codes will invalidate all existing codes. Make sure to save the new codes securely.
                            </p>
                        </div>
                    </div>
                </div>

                <p class="text-gray-300 text-sm">
                    This action will:
                </p>
                <ul class="text-gray-400 text-sm mt-2 space-y-1 ml-4">
                    <li>• Generate 10 new backup codes</li>
                    <li>• Invalidate all existing backup codes</li>
                    <li>• Display the new codes for you to save</li>
                </ul>
            </div>

            <div class="flex gap-3">
                <button type="button" onclick="closeModal('generateBackupCodesModal')"
                        class="flex-1 bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors">
                    Cancel
                </button>
                <button type="submit"
                        class="flex-1 bg-yellow-600 text-white py-2 px-4 rounded-lg hover:bg-yellow-700 transition-colors">
                    Generate Codes
                </button>
            </div>
        </form>
    </div>
</div>

<?php if ($newBackupCodes): ?>
<!-- New Backup Codes Display Modal -->
<div id="newBackupCodesModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-secondary-800 rounded-lg p-6 w-full max-w-lg mx-4">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-bold text-white">Your New Backup Codes</h3>
            <button onclick="closeModal('newBackupCodesModal')" class="text-gray-400 hover:text-white">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <div class="mb-4">
            <div class="bg-red-500/20 border border-red-500/50 rounded-lg p-4 mb-4">
                <p class="text-red-200 text-sm">
                    <strong>Important:</strong> Save these codes in a secure location. They will not be shown again and each code can only be used once.
                </p>
            </div>

            <div class="grid grid-cols-2 gap-2">
                <?php foreach ($newBackupCodes as $code): ?>
                    <div class="bg-secondary-700 rounded p-2 text-center font-mono text-salon-gold">
                        <?= htmlspecialchars($code) ?>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <div class="flex gap-3">
            <button onclick="printBackupCodes()" class="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                Print Codes
            </button>
            <button onclick="closeModal('newBackupCodesModal')" class="flex-1 bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors">
                I've Saved Them
            </button>
        </div>
    </div>
</div>

<script>
// Auto-show new backup codes modal
document.addEventListener('DOMContentLoaded', function() {
    openModal('newBackupCodesModal');
});

function printBackupCodes() {
    window.print();
}
</script>
<?php endif; ?>

<script>
function openModal(modalId) {
    document.getElementById(modalId).classList.remove('hidden');
}

function closeModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
}

// Close modal when clicking outside
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('fixed')) {
        e.target.classList.add('hidden');
    }
});

// Admin management functions
function toggleAdminStatus(adminId, activate) {
    if (confirm(`Are you sure you want to ${activate ? 'activate' : 'deactivate'} this admin user?`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="toggle_admin_status">
            <input type="hidden" name="admin_id" value="${adminId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

function editAdmin(adminId, name, email, phone, dateOfBirth) {
    document.getElementById('editAdminId').value = adminId;
    document.getElementById('editAdminName').value = name;
    document.getElementById('editAdminEmail').value = email;
    document.getElementById('editAdminPhone').value = phone || '';
    document.getElementById('editAdminDateOfBirth').value = dateOfBirth || '';
    openModal('editAdminModal');
}

function resetAdminPassword(adminId, adminName) {
    document.getElementById('resetPasswordAdminId').value = adminId;
    document.getElementById('resetPasswordAdminName').value = adminName;
    openModal('resetPasswordModal');
}

function deleteAdmin(adminId, adminName) {
    if (confirm(`Are you sure you want to delete admin user "${adminName}"? This action will deactivate their account.`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete_admin">
            <input type="hidden" name="admin_id" value="${adminId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// Search and filter functionality
function filterAdmins() {
    const searchTerm = document.getElementById('adminSearch').value.toLowerCase();
    const statusFilter = document.getElementById('statusFilter').value;
    const adminCards = document.querySelectorAll('.admin-card');

    adminCards.forEach(card => {
        const name = card.dataset.name.toLowerCase();
        const email = card.dataset.email.toLowerCase();
        const status = card.dataset.status;

        const matchesSearch = name.includes(searchTerm) || email.includes(searchTerm);
        const matchesStatus = statusFilter === 'all' || status === statusFilter;

        if (matchesSearch && matchesStatus) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}

// Real-time search
document.getElementById('adminSearch').addEventListener('input', filterAdmins);
document.getElementById('statusFilter').addEventListener('change', filterAdmins);

// Activity log toggle
function toggleActivityLog() {
    const container = document.getElementById('activityLogContainer');
    const button = event.target;

    if (container.classList.contains('hidden')) {
        container.classList.remove('hidden');
        button.innerHTML = '<i class="fas fa-eye-slash mr-2"></i>Hide Logs';
    } else {
        container.classList.add('hidden');
        button.innerHTML = '<i class="fas fa-history mr-2"></i>View All Logs';
    }
}

// Password confirmation validation
document.querySelector('input[name="confirm_password"]').addEventListener('input', function() {
    const newPassword = document.querySelector('input[name="new_password"]').value;
    const confirmPassword = this.value;

    if (newPassword !== confirmPassword) {
        this.setCustomValidity('Passwords do not match');
    } else {
        this.setCustomValidity('');
    }
});
</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>
