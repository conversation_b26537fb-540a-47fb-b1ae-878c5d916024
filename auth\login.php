<?php
/**
 * Login Page
 * Flix Salon & SPA - PHP Version
 */

require_once __DIR__ . '/../config/app.php';

// Redirect if already logged in
if (isLoggedIn()) {
    $user = getCurrentUser();
    if ($user['role'] === 'ADMIN') {
        redirect('/admin');
    } elseif ($user['role'] === 'STAFF') {
        redirect('/staff');
    } else {
        redirect('/customer');
    }
}

$error = '';
$success = '';

// Check for password reset success message
if (isset($_SESSION['password_reset_success'])) {
    $success = $_SESSION['password_reset_success'];
    unset($_SESSION['password_reset_success']);
}

// Get redirect parameter
$redirectUrl = $_GET['redirect'] ?? '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = sanitize($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $redirectUrl = sanitize($_POST['redirect'] ?? '');

    if (empty($email) || empty($password)) {
        $error = 'Please fill in all fields';
    } else {
        $result = $auth->login($email, $password);

        if ($result['success']) {
            $user = $result['user'];

            // Debug: Log successful login attempt (remove in production)
            if (!isProduction()) {
                error_log("Login successful for user: " . $email . " with role: " . $user['role']);
            }

<<<<<<< HEAD
            // Handle redirect after successful login
=======
            // Check if 2FA is required
            if (isset($result['requires_2fa']) && $result['requires_2fa']) {
                // Redirect to 2FA verification page
                redirect('/admin/auth/verify-2fa.php');
            }

            // Handle redirect after successful login (non-2FA users)
>>>>>>> 837440c (By Chaz)
            if (!empty($redirectUrl) && $user['role'] === 'CUSTOMER') {
                // Validate redirect URL to prevent open redirect attacks
                $allowedPaths = ['/customer/', '/services.php'];
                $isValidRedirect = false;

                foreach ($allowedPaths as $allowedPath) {
                    if (strpos($redirectUrl, $allowedPath) !== false) {
                        $isValidRedirect = true;
                        break;
                    }
                }

                if ($isValidRedirect) {
                    redirect($redirectUrl);
                } else {
                    redirect('/customer');
                }
            } else {
                // Default redirect based on role
                if ($user['role'] === 'ADMIN') {
                    redirect('/admin');
                } elseif ($user['role'] === 'STAFF') {
                    redirect('/staff');
                } else {
                    redirect('/customer');
                }
            }
        } else {
            $error = $result['error'];

            // Debug: Log failed login attempt (remove in production)
            if (!isProduction()) {
                error_log("Login failed for user: " . $email . " - Error: " . $result['error']);
            }
        }
    }
}

$pageTitle = "Login";
$pageDescription = "Login to your Flix Salon & SPA account";

// Include header
include __DIR__ . '/../includes/header.php';
?>
<!-- Enhanced Auth Page Styles - Unified Design -->
<style>
    /* Auth page background with gradient overlay */
    .auth-main {
        min-height: calc(100vh - 140px);
        background: linear-gradient(135deg, #000000 0%, #0a0a0a 50%, #141414 100%);
        background-attachment: fixed;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 3rem 1rem;
    }

    .auth-main::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(circle at 30% 20%, rgba(245, 158, 11, 0.1) 0%, transparent 50%),
                    radial-gradient(circle at 70% 80%, rgba(245, 158, 11, 0.05) 0%, transparent 50%);
        pointer-events: none;
    }

    /* Glass container with enhanced effects */
    .auth-container {
        background: rgba(10, 10, 10, 0.85);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(245, 158, 11, 0.2);
        box-shadow:
            0 25px 50px -12px rgba(0, 0, 0, 0.8),
            0 0 0 1px rgba(245, 158, 11, 0.1),
            inset 0 1px 0 rgba(245, 158, 11, 0.1);
        position: relative;
        overflow: hidden;
    }

    .auth-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg, transparent, rgba(245, 158, 11, 0.5), transparent);
    }

    /* Enhanced input styling */
    .input-group {
        position: relative;
    }

    .input-group input {
        background: rgba(20, 20, 20, 0.8) !important;
        border: 1px solid rgba(75, 85, 99, 0.5) !important;
        backdrop-filter: blur(10px);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .input-group input:focus {
        background: rgba(20, 20, 20, 0.95) !important;
        border-color: rgba(245, 158, 11, 0.8) !important;
        box-shadow:
            0 0 0 3px rgba(245, 158, 11, 0.1),
            0 4px 12px rgba(245, 158, 11, 0.15);
        transform: translateY(-1px);
    }

    .input-group input:focus + .input-border {
        transform: scaleX(1);
    }

    .input-border {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(90deg, #f59e0b, #fbbf24, #f59e0b);
        transform: scaleX(0);
        transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        border-radius: 1px;
    }

    /* Enhanced button styling */
    .btn-primary {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 50%, #f59e0b 100%);
        background-size: 200% 200%;
        border: 1px solid rgba(245, 158, 11, 0.3);
        box-shadow:
            0 4px 15px rgba(245, 158, 11, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
    }

    .btn-primary::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s ease;
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 50%, #fbbf24 100%);
        background-position: 100% 0;
        transform: translateY(-2px);
        box-shadow:
            0 8px 25px rgba(245, 158, 11, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        border-color: rgba(245, 158, 11, 0.6);
    }

    .btn-primary:hover::before {
        left: 100%;
    }

    .btn-primary:active {
        transform: translateY(0);
        box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
    }

    /* Enhanced animations */
    .error-message, .success-message {
        animation: slideInEnhanced 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    @keyframes slideInEnhanced {
        from {
            opacity: 0;
            transform: translateY(-20px) scale(0.95);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }

    /* Enhanced checkbox styling */
    input[type="checkbox"] {
        background: rgba(20, 20, 20, 0.8);
        border: 1px solid rgba(75, 85, 99, 0.5);
        backdrop-filter: blur(10px);
    }

    input[type="checkbox"]:checked {
        background: linear-gradient(135deg, #f59e0b, #d97706);
        border-color: #f59e0b;
    }

    /* Link enhancements */
    a {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    a:hover {
        text-shadow: 0 0 8px rgba(245, 158, 11, 0.5);
    }
</style>

<!-- Auth Page Content -->
<div class="auth-main">
    <div class="max-w-md w-full space-y-8">
        <div class="auth-container auth-card rounded-2xl p-8 sm:p-10">
            <div class="text-center mb-8">
                <h2 class="text-2xl sm:text-3xl font-bold text-white mb-2">Welcome Back</h2>
                <p class="text-gray-300 text-sm sm:text-base">
                    Sign in to your account to continue your beauty journey
                </p>
            </div>

            <form class="space-y-6" method="POST" autocomplete="off">
                <!-- Hidden redirect field -->
                <?php if (!empty($redirectUrl)): ?>
                    <input type="hidden" name="redirect" value="<?= htmlspecialchars($redirectUrl) ?>">
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="error-message bg-red-500/10 border border-red-500/30 rounded-xl p-4 backdrop-blur-sm">
                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-red-400 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-400 mb-1">Authentication Error</h3>
                                <p class="text-sm text-red-300"><?= htmlspecialchars($error) ?></p>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if ($success): ?>
                    <div class="error-message bg-green-500/10 border border-green-500/30 rounded-xl p-4 backdrop-blur-sm">
                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-green-400 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-green-400 mb-1">Success</h3>
                                <p class="text-sm text-green-300"><?= htmlspecialchars($success) ?></p>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <div class="space-y-6">
                    <div class="input-group">
                        <label for="email" class="block text-sm font-semibold text-gray-200 mb-2">
                            Email Address
                        </label>
                        <div class="relative">
                            <input id="email" name="email" type="email" autocomplete="off" required
                                   value="<?= htmlspecialchars($_POST['email'] ?? '') ?>"
                                   class="appearance-none relative block w-full px-4 py-3 border border-gray-600 placeholder-gray-400 text-white bg-gray-800/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-salon-gold/50 focus:border-salon-gold transition-all duration-300 text-sm backdrop-blur-sm"
                                   placeholder="Enter your email address">
                            <div class="input-border"></div>
                        </div>
                    </div>

                    <div class="input-group">
                        <label for="password" class="block text-sm font-semibold text-gray-200 mb-2">
                            Password
                        </label>
                        <div class="relative">
                            <input id="password" name="password" type="password" autocomplete="off" required
                                   class="appearance-none relative block w-full px-4 py-3 pr-12 border border-gray-600 placeholder-gray-400 text-white bg-gray-800/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-salon-gold/50 focus:border-salon-gold transition-all duration-300 text-sm backdrop-blur-sm"
                                   placeholder="Enter your password">
                            <button type="button" class="absolute inset-y-0 right-0 pr-4 flex items-center hover:text-salon-gold transition-colors" onclick="togglePassword()">
                                <svg id="eye-icon" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                </svg>
                            </button>
                            <div class="input-border"></div>
                        </div>
                    </div>
                </div>

                <div class="flex items-center justify-between py-2">
                    <div class="flex items-center">
                        <input id="remember-me" name="remember-me" type="checkbox"
                               class="h-4 w-4 text-salon-gold focus:ring-salon-gold border-gray-600 bg-gray-800 rounded transition-colors">
                        <label for="remember-me" class="ml-3 block text-sm text-gray-300 select-none cursor-pointer">
                            Remember me for 2 weeks
                        </label>
                    </div>

                    <div class="text-sm">
                        <a href="<?= getBasePath() ?>/auth/forgot-password.php" class="font-medium text-salon-gold hover:text-gold-light transition-colors">
                            Forgot password?
                        </a>
                    </div>
                </div>

                <div class="pt-2">
                    <button type="submit"
                            class="btn-primary group relative w-full flex justify-center py-3 px-6 border border-transparent text-sm font-semibold rounded-xl text-black focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-salon-gold focus:ring-offset-gray-800">
                        <span class="absolute left-0 inset-y-0 flex items-center pl-4">
                            <svg class="h-5 w-5 text-black/80 group-hover:text-black transition-colors" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
                            </svg>
                        </span>
                        Sign In to Your Account
                    </button>
                </div>

                <div class="text-center pt-6">
                    <p class="text-sm text-gray-300">
                        Don't have an account?
                        <a href="<?= getBasePath() ?>/auth/register.php<?= !empty($redirectUrl) ? '?redirect=' . urlencode($redirectUrl) : '' ?>"
                           class="font-semibold text-salon-gold hover:text-gold-light transition-colors ml-1">
                            Create one here
                        </a>
                    </p>
                </div>
            </form>

        </div>
    </div>
</div>

<script>
    function togglePassword() {
        const passwordInput = document.getElementById('password');
        const eyeIcon = document.getElementById('eye-icon');

        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            eyeIcon.innerHTML = `
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
            `;
        } else {
            passwordInput.type = 'password';
            eyeIcon.innerHTML = `
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 616 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            `;
        }
    }
</script>

<?php
// Include footer
include __DIR__ . '/../includes/footer.php';
?>
